package action

import (
	"fmt"
	"time"

	"auto-register/logger"
)

// ButtonElementAction 基础按钮元素操作
type ButtonElementAction struct {
	Selector       string
	WaitTimeout    time.Duration
	TakeScreenshot bool
	ScreenshotName string
	WaitAfterClick time.Duration
}

// NewButtonElementAction 创建基础按钮元素操作
func NewButtonElementAction(selector string) *ButtonElementAction {
	return &ButtonElementAction{
		Selector:       selector,
		WaitTimeout:    5 * time.Second,
		TakeScreenshot: false,
		ScreenshotName: "button_clicked",
		WaitAfterClick: 1 * time.Second,
	}
}

// WithTimeout 设置等待超时时间
func (a *ButtonElementAction) WithTimeout(timeout time.Duration) *ButtonElementAction {
	a.WaitTimeout = timeout
	return a
}

// WithScreenshot 设置截图选项
func (a *ButtonElementAction) WithScreenshot(take bool, name string) *ButtonElementAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// WithWaitAfterClick 设置点击后等待时间
func (a *ButtonElementAction) WithWaitAfterClick(duration time.Duration) *ButtonElementAction {
	a.WaitAfterClick = duration
	return a
}

// Execute 执行按钮点击操作
func (a *ButtonElementAction) Execute(ctx *ActionContext) error {
	logger.Debugf("执行按钮点击操作，选择器: %s", a.Selector)

	// 等待按钮出现
	if err := ctx.Manager.WaitForElement(a.Selector, a.WaitTimeout); err != nil {
		ctx.Manager.TakeScreenshot("button_element_not_found")
		return fmt.Errorf("未找到按钮元素 %s: %w", a.Selector, err)
	}

	// 点击按钮
	if err := ctx.Manager.Click(a.Selector); err != nil {
		return fmt.Errorf("点击按钮失败: %w", err)
	}

	logger.Info("按钮点击成功")

	// 等待点击后的处理
	if a.WaitAfterClick > 0 {
		time.Sleep(a.WaitAfterClick)
	}

	// 截图
	if a.TakeScreenshot {
		if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
			logger.Warnf("截图失败: %v", err)
		}
	}

	return nil
}

// GetName 获取操作名称
func (a *ButtonElementAction) GetName() string {
	return "ButtonElement"
}

// ButtonClickAction 按钮点击操作（支持多选择器和JavaScript回退）
type ButtonClickAction struct {
	Selectors          ElementSelectors
	JavaScriptFallback bool
	JSSearchTexts      []string
	WaitTimeout        time.Duration
	TakeScreenshot     bool
	ScreenshotName     string
	WaitAfterClick     time.Duration
}

// NewButtonClickAction 创建按钮点击操作
func NewButtonClickAction(selectors ElementSelectors) *ButtonClickAction {
	return &ButtonClickAction{
		Selectors:          selectors,
		JavaScriptFallback: true,
		JSSearchTexts:      []string{},
		WaitTimeout:        5 * time.Second,
		TakeScreenshot:     false,
		ScreenshotName:     "button_clicked",
		WaitAfterClick:     1 * time.Second,
	}
}

// WithJavaScriptFallback 设置JavaScript回退选项
func (a *ButtonClickAction) WithJavaScriptFallback(enabled bool, searchTexts []string) *ButtonClickAction {
	a.JavaScriptFallback = enabled
	a.JSSearchTexts = searchTexts
	return a
}

// WithTimeout 设置等待超时时间
func (a *ButtonClickAction) WithTimeout(timeout time.Duration) *ButtonClickAction {
	a.WaitTimeout = timeout
	return a
}

// WithScreenshot 设置截图选项
func (a *ButtonClickAction) WithScreenshot(take bool, name string) *ButtonClickAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// WithWaitAfterClick 设置点击后等待时间
func (a *ButtonClickAction) WithWaitAfterClick(duration time.Duration) *ButtonClickAction {
	a.WaitAfterClick = duration
	return a
}

// Execute 执行按钮点击操作
func (a *ButtonClickAction) Execute(ctx *ActionContext) error {
	logger.Debug("执行按钮点击操作")

	// 首先尝试使用CSS选择器
	for _, selector := range a.Selectors {
		if ctx.Manager.IsElementPresent(selector) {
			logger.Debugf("找到按钮: %s", selector)
			if err := ctx.Manager.Click(selector); err != nil {
				logger.Debugf("点击按钮失败: %s, 错误: %v", selector, err)
				continue
			}
			logger.Info("按钮点击成功")

			// 等待点击后的处理
			if a.WaitAfterClick > 0 {
				time.Sleep(a.WaitAfterClick)
			}

			// 截图
			if a.TakeScreenshot {
				if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
					logger.Warnf("截图失败: %v", err)
				}
			}

			return nil
		}
	}

	// 如果CSS选择器失败，尝试JavaScript回退
	if a.JavaScriptFallback {
		if err := a.clickWithJavaScript(ctx); err == nil {
			logger.Info("通过JavaScript点击按钮成功")

			// 等待点击后的处理
			if a.WaitAfterClick > 0 {
				time.Sleep(a.WaitAfterClick)
			}

			// 截图
			if a.TakeScreenshot {
				if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
					logger.Warnf("截图失败: %v", err)
				}
			}

			return nil
		}
	}

	ctx.Manager.TakeScreenshot("button_not_found")
	return fmt.Errorf("未找到可点击的按钮")
}

// clickWithJavaScript 使用JavaScript点击按钮
func (a *ButtonClickAction) clickWithJavaScript(ctx *ActionContext) error {
	// 构建搜索文本条件
	textConditions := ""
	for i, text := range a.JSSearchTexts {
		if i > 0 {
			textConditions += " || "
		}
		textConditions += fmt.Sprintf("text.toLowerCase().includes('%s')", text)
	}

	// 如果没有指定搜索文本，使用默认的submit类型检查
	if textConditions == "" {
		textConditions = "btn.type === 'submit'"
	}

	script := fmt.Sprintf(`
		const buttons = document.querySelectorAll('button, input[type="submit"]');
		for (let btn of buttons) {
			const text = btn.textContent || btn.value || '';
			if (%s) {
				btn.click();
				return { success: true, text: text };
			}
		}
		return { success: false };
	`, textConditions)

	var result map[string]interface{}
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("执行JavaScript失败: %w", err)
	}

	if success, ok := result["success"].(bool); ok && success {
		return nil
	}

	return fmt.Errorf("JavaScript未找到匹配的按钮")
}

// GetName 获取操作名称
func (a *ButtonClickAction) GetName() string {
	return "ButtonClick"
}

// ContinueButtonAction 继续按钮点击操作
type ContinueButtonAction struct {
	*ButtonClickAction
}

// NewContinueButtonAction 创建继续按钮点击操作
func NewContinueButtonAction() *ContinueButtonAction {
	buttonAction := NewButtonClickAction(ContinueButtonSelectors)
	buttonAction.JSSearchTexts = []string{"continue", "next", "继续", "下一步", "submit"}
	buttonAction.ScreenshotName = "continue_button_clicked"
	buttonAction.WaitAfterClick = 2 * time.Second

	return &ContinueButtonAction{
		ButtonClickAction: buttonAction,
	}
}

// Execute 执行继续按钮点击操作
func (a *ContinueButtonAction) Execute(ctx *ActionContext) error {
	logger.Debug("查找继续按钮")

	if err := a.ButtonClickAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击继续按钮失败: %w", err)
	}

	logger.Info("继续按钮点击完成")
	return nil
}

// GetName 获取操作名称
func (a *ContinueButtonAction) GetName() string {
	return "ContinueButton"
}

// LoginButtonAction 登录按钮点击操作
type LoginButtonAction struct {
	*ButtonClickAction
}

// NewLoginButtonAction 创建登录按钮点击操作
func NewLoginButtonAction() *LoginButtonAction {
	buttonAction := NewButtonClickAction(LoginButtonSelectors)
	buttonAction.JSSearchTexts = []string{"log in", "login", "sign in", "登录", "submit"}
	buttonAction.ScreenshotName = "login_button_clicked"
	buttonAction.WaitAfterClick = 3 * time.Second

	return &LoginButtonAction{
		ButtonClickAction: buttonAction,
	}
}

// Execute 执行登录按钮点击操作
func (a *LoginButtonAction) Execute(ctx *ActionContext) error {
	logger.Debug("查找登录按钮")

	if err := a.ButtonClickAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击登录按钮失败: %w", err)
	}

	logger.Info("登录按钮点击完成")
	return nil
}

// GetName 获取操作名称
func (a *LoginButtonAction) GetName() string {
	return "LoginButton"
}
