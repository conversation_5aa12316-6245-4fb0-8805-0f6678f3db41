package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config 配置结构体
type Config struct {
	// 域名配置
	Domain string

	// 临时邮箱配置
	TempMail     string
	TempMailEpin string
	TempMailExt  string

	// IMAP配置
	IMAPServer   string
	IMAPPort     int
	IMAPUser     string
	IMAPPass     string
	IMAPDir      string
	IMAPProtocol string

	// 浏览器配置
	BrowserUserAgent string
	BrowserHeadless  bool
	BrowserPath      string
	BrowserProxy     string

	// 日志配置
	LogLevel string
	LogFile  string

	// 注册配置
	MaxRetries          int
	RetryInterval       int
	VerificationTimeout int
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	// 加载 .env 文件
	if err := godotenv.Load(); err != nil {
		// .env 文件不存在时不报错，使用环境变量
		fmt.Println("Warning: .env file not found, using environment variables")
	}

	config := &Config{
		Domain:              getEnv("DOMAIN", ""),
		TempMail:            getEnv("TEMP_MAIL", ""),
		TempMailEpin:        getEnv("TEMP_MAIL_EPIN", ""),
		TempMailExt:         getEnv("TEMP_MAIL_EXT", "@mailto.plus"),
		IMAPServer:          getEnv("IMAP_SERVER", ""),
		IMAPPort:            getEnvInt("IMAP_PORT", 993),
		IMAPUser:            getEnv("IMAP_USER", ""),
		IMAPPass:            getEnv("IMAP_PASS", ""),
		IMAPDir:             getEnv("IMAP_DIR", "inbox"),
		IMAPProtocol:        getEnv("IMAP_PROTOCOL", "IMAP"),
		BrowserUserAgent:    getEnv("BROWSER_USER_AGENT", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.92 Safari/537.36"),
		BrowserHeadless:     getEnvBool("BROWSER_HEADLESS", true),
		BrowserPath:         getEnv("BROWSER_PATH", ""),
		BrowserProxy:        getEnv("BROWSER_PROXY", ""),
		LogLevel:            getEnv("LOG_LEVEL", "info"),
		LogFile:             getEnv("LOG_FILE", "auto-register.log"),
		MaxRetries:          getEnvInt("MAX_RETRIES", 3),
		RetryInterval:       getEnvInt("RETRY_INTERVAL", 60),
		VerificationTimeout: getEnvInt("VERIFICATION_TIMEOUT", 300),
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Domain == "" {
		return fmt.Errorf("DOMAIN 配置不能为空")
	}

	// 检查邮箱配置
	if c.TempMail == "null" || c.TempMail == "" {
		// IMAP 模式
		if c.IMAPServer == "" {
			return fmt.Errorf("IMAP_SERVER 配置不能为空")
		}
		if c.IMAPUser == "" {
			return fmt.Errorf("IMAP_USER 配置不能为空")
		}
		if c.IMAPPass == "" {
			return fmt.Errorf("IMAP_PASS 配置不能为空")
		}
	} else {
		// 临时邮箱模式
		if c.TempMailEpin == "" {
			return fmt.Errorf("TEMP_MAIL_EPIN 配置不能为空")
		}
	}

	return nil
}

// IsIMAPMode 是否为IMAP模式
func (c *Config) IsIMAPMode() bool {
	return c.TempMail == "null" || c.TempMail == ""
}

// PrintConfig 打印配置信息
func (c *Config) PrintConfig() {
	fmt.Println("=== 配置信息 ===")
	fmt.Printf("域名: %s\n", c.Domain)

	if c.IsIMAPMode() {
		fmt.Printf("邮箱模式: IMAP\n")
		fmt.Printf("IMAP服务器: %s:%d\n", c.IMAPServer, c.IMAPPort)
		fmt.Printf("IMAP用户: %s\n", c.IMAPUser)
		fmt.Printf("IMAP密码: %s\n", strings.Repeat("*", len(c.IMAPPass)))
		fmt.Printf("IMAP目录: %s\n", c.IMAPDir)
	} else {
		fmt.Printf("邮箱模式: 临时邮箱\n")
		fmt.Printf("临时邮箱: %s%s\n", c.TempMail, c.TempMailExt)
	}

	fmt.Printf("浏览器无头模式: %t\n", c.BrowserHeadless)
	fmt.Printf("日志级别: %s\n", c.LogLevel)
	fmt.Println("================")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整型环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
