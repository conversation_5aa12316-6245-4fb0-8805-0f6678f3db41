package step

import (
	"fmt"
	"time"

	"auto-register/browser/automation/action"
	"auto-register/logger"
)

// NavigateToStep 导航到页面步骤
type NavigateToStep struct {
	URL            string
	WaitTime       time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewNavigateToStep 创建导航到页面步骤
func NewNavigateToStep(url string) *NavigateToStep {
	return &NavigateToStep{
		URL:            url,
		WaitTime:       3 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "page_loaded",
	}
}

// WithWaitTime 设置等待时间
func (s *NavigateToStep) WithWaitTime(duration time.Duration) *NavigateToStep {
	s.WaitTime = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *NavigateToStep) WithScreenshot(take bool, name string) *NavigateToStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行导航到页面步骤
func (s *NavigateToStep) Execute(ctx *action.ActionContext) error {
	logger.Infof("执行导航到页面步骤: %s", s.URL)

	navigateAction := action.NewNavigateAction(s.URL).
		WithWaitTime(s.WaitTime).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := navigateAction.Execute(ctx); err != nil {
		return fmt.Errorf("导航到页面失败: %w", err)
	}

	logger.Info("页面导航完成")
	return nil
}

// GetName 获取步骤名称
func (s *NavigateToStep) GetName() string {
	return "NavigateTo"
}

// WaitForPageLoadStep 等待页面加载步骤
type WaitForPageLoadStep struct {
	Timeout time.Duration
}

// NewWaitForPageLoadStep 创建等待页面加载步骤
func NewWaitForPageLoadStep() *WaitForPageLoadStep {
	return &WaitForPageLoadStep{
		Timeout: 30 * time.Second,
	}
}

// WithTimeout 设置超时时间
func (s *WaitForPageLoadStep) WithTimeout(timeout time.Duration) *WaitForPageLoadStep {
	s.Timeout = timeout
	return s
}

// Execute 执行等待页面加载步骤
func (s *WaitForPageLoadStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行等待页面加载步骤")

	waitAction := action.NewWaitForPageLoadAction(s.Timeout)

	if err := waitAction.Execute(ctx); err != nil {
		return fmt.Errorf("等待页面加载失败: %w", err)
	}

	logger.Info("页面加载完成")
	return nil
}

// GetName 获取步骤名称
func (s *WaitForPageLoadStep) GetName() string {
	return "WaitForPageLoad"
}

// NavigateToLoginPageStep 导航到登录页面步骤
type NavigateToLoginPageStep struct {
	BaseURL        string
	WaitTime       time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewNavigateToLoginPageStep 创建导航到登录页面步骤
func NewNavigateToLoginPageStep(baseURL string) *NavigateToLoginPageStep {
	return &NavigateToLoginPageStep{
		BaseURL:        baseURL,
		WaitTime:       3 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "login_page_loaded",
	}
}

// WithWaitTime 设置等待时间
func (s *NavigateToLoginPageStep) WithWaitTime(duration time.Duration) *NavigateToLoginPageStep {
	s.WaitTime = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *NavigateToLoginPageStep) WithScreenshot(take bool, name string) *NavigateToLoginPageStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行导航到登录页面步骤
func (s *NavigateToLoginPageStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行导航到登录页面步骤")

	// 构建登录页面URL
	loginURL := s.BaseURL
	if loginURL == "" {
		return fmt.Errorf("基础URL不能为空")
	}

	navigateAction := action.NewNavigateAction(loginURL).
		WithWaitTime(s.WaitTime).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := navigateAction.Execute(ctx); err != nil {
		return fmt.Errorf("导航到登录页面失败: %w", err)
	}

	logger.Info("登录页面导航完成")
	return nil
}

// GetName 获取步骤名称
func (s *NavigateToLoginPageStep) GetName() string {
	return "NavigateToLoginPage"
}

// NavigateToRegisterPageStep 导航到注册页面步骤
type NavigateToRegisterPageStep struct {
	BaseURL        string
	WaitTime       time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewNavigateToRegisterPageStep 创建导航到注册页面步骤
func NewNavigateToRegisterPageStep(baseURL string) *NavigateToRegisterPageStep {
	return &NavigateToRegisterPageStep{
		BaseURL:        baseURL,
		WaitTime:       3 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "register_page_loaded",
	}
}

// WithWaitTime 设置等待时间
func (s *NavigateToRegisterPageStep) WithWaitTime(duration time.Duration) *NavigateToRegisterPageStep {
	s.WaitTime = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *NavigateToRegisterPageStep) WithScreenshot(take bool, name string) *NavigateToRegisterPageStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行导航到注册页面步骤
func (s *NavigateToRegisterPageStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行导航到注册页面步骤")

	// 构建注册页面URL
	registerURL := s.BaseURL
	if registerURL == "" {
		return fmt.Errorf("基础URL不能为空")
	}

	navigateAction := action.NewNavigateAction(registerURL).
		WithWaitTime(s.WaitTime).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := navigateAction.Execute(ctx); err != nil {
		return fmt.Errorf("导航到注册页面失败: %w", err)
	}

	logger.Info("注册页面导航完成")
	return nil
}

// GetName 获取步骤名称
func (s *NavigateToRegisterPageStep) GetName() string {
	return "NavigateToRegisterPage"
}
