# Auto Register Framework 使用指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Go 1.21 或更高版本
- Chrome/Chromium 浏览器

### 2. 下载和构建

```bash
# 克隆项目
git clone <repository-url>
cd auto-register

# 安装依赖
make deps

# 构建项目
make build
```

### 3. 配置环境

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 运行程序

```bash
# 使用 make 运行
make run

# 或直接运行可执行文件
./bin/auto-register
```

## 配置说明

### 基础配置

```env
# 必填：用于生成邮箱的域名
DOMAIN=your-domain.com
```

### 邮箱配置（二选一）

#### 选项1：临时邮箱模式

```env
TEMP_MAIL=your-username
TEMP_MAIL_EPIN=your-pin
TEMP_MAIL_EXT=@mailto.plus
```

#### 选项2：IMAP邮箱模式

```env
TEMP_MAIL=null  # 设置为null启用IMAP模式
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password
IMAP_DIR=inbox
```

### 浏览器配置

```env
# 是否使用无头模式（推荐生产环境使用true）
BROWSER_HEADLESS=true

# 自定义浏览器路径（可选）
BROWSER_PATH=/path/to/chrome

# 代理设置（可选）
BROWSER_PROXY=http://proxy:port
```

## 使用场景

### 场景1：仅重置机器码

如果您只需要重置设备标识：

1. 启动程序
2. 选择选项 `1`
3. 等待重置完成

### 场景2：完整注册流程

如果您需要注册新账号：

1. 确保邮箱配置正确
2. 启动程序
3. 选择选项 `2`
4. 程序将自动：
   - 生成随机账号信息
   - 打开浏览器并填写注册表单
   - 处理验证码
   - 接收邮箱验证码
   - 更新本地认证信息

## 邮箱配置详解

### Gmail配置示例

```env
TEMP_MAIL=null
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password  # 需要开启两步验证并生成应用密码
IMAP_DIR=inbox
```

### QQ邮箱配置示例

```env
TEMP_MAIL=null
IMAP_SERVER=imap.qq.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-authorization-code  # QQ邮箱授权码
IMAP_DIR=inbox
```

### 163邮箱配置示例

```env
TEMP_MAIL=null
IMAP_SERVER=imap.163.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-authorization-code  # 163邮箱授权码
IMAP_DIR=inbox
```

## 故障排除

### 常见问题

#### 1. 浏览器启动失败

**错误信息**：`启动浏览器失败`

**解决方案**：
- 确保Chrome/Chromium已正确安装
- 尝试指定浏览器路径：`BROWSER_PATH=/usr/bin/google-chrome`
- 检查是否有足够的系统权限

#### 2. 邮箱验证失败

**错误信息**：`获取验证码失败`

**解决方案**：
- 检查IMAP配置是否正确
- 确认邮箱授权码是否有效
- 检查网络连接
- 确认邮箱服务商的IMAP设置

#### 3. 验证码识别失败

**错误信息**：`Turnstile验证失败`

**解决方案**：
- 检查网络连接稳定性
- 尝试使用代理
- 查看screenshots目录下的截图文件

#### 4. 配置文件错误

**错误信息**：`配置验证失败`

**解决方案**：
- 检查.env文件格式
- 确认必填字段已正确填写
- 参考.env.example文件

### 调试模式

启用调试模式获取更详细的日志：

```env
LOG_LEVEL=debug
```

查看日志文件：

```bash
tail -f auto-register.log
```

### 截图调试

程序会在screenshots目录下保存关键步骤的截图，可用于调试：

- `turnstile_start.png` - 验证码页面初始状态
- `turnstile_clicked.png` - 点击验证码后状态
- `turnstile_success.png` - 验证成功状态
- `turnstile_failed.png` - 验证失败状态

## 高级配置

### 性能调优

```env
# 增加重试次数
MAX_RETRIES=5

# 调整重试间隔（秒）
RETRY_INTERVAL=30

# 调整验证超时时间（秒）
VERIFICATION_TIMEOUT=600
```

### 安全设置

```env
# 使用无头模式（推荐）
BROWSER_HEADLESS=true

# 使用代理保护隐私
BROWSER_PROXY=socks5://127.0.0.1:1080
```

## 注意事项

1. **合法使用**：请确保在合法合规的前提下使用本工具
2. **频率控制**：避免过于频繁的注册操作，建议间隔使用
3. **邮箱安全**：使用IMAP时建议使用专门的邮箱账号
4. **网络环境**：确保网络连接稳定，必要时配置代理
5. **浏览器版本**：保持Chrome/Chromium为最新版本

## 技术支持

如果遇到问题，请：

1. 查看日志文件：`auto-register.log`
2. 检查截图文件：`screenshots/`目录
3. 参考本文档的故障排除部分
4. 提交Issue时请附上相关日志和错误信息
