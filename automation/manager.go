package automation

import (
	"fmt"
	"time"

	"auto-register/browser"
	"auto-register/browser/automation/action"
	"auto-register/config"
	"auto-register/generator"
	"auto-register/logger"
)

// Manager 自动化管理器
type Manager struct {
	config         *config.Config
	browserManager *browser.Manager
	ctx            *action.ActionContext
	timeoutManager *TimeoutManager
	flowFactory    *FlowFactory
}

// NewManager 创建自动化管理器
func NewManager(cfg *config.Config, browserManager *browser.Manager) *Manager {
	ctx := action.NewActionContext(browserManager)
	timeoutManager := NewTimeoutManager(cfg)
	flowFactory := NewFlowFactory(timeoutManager)

	return &Manager{
		config:         cfg,
		browserManager: browserManager,
		ctx:            ctx,
		timeoutManager: timeoutManager,
		flowFactory:    flowFactory,
	}
}

// ExecuteFlow 执行指定的自动化流程
func (m *Manager) ExecuteFlow(account *generator.AccountInfo) error {
	logger.Infof("开始执行自动化流程: %s", m.config.AutomationFlow)

	switch m.config.AutomationFlow {
	case "cursor_register":
		return m.executeCursorRegisterFlow(account)
	case "augment_login":
		return m.executeAugmentLoginFlow(account)
	case "custom":
		return m.executeCustomFlow(account)
	default:
		return fmt.Errorf("不支持的自动化流程: %s", m.config.AutomationFlow)
	}
}

// executeCursorRegisterFlow 执行Cursor注册流程
func (m *Manager) executeCursorRegisterFlow(account *generator.AccountInfo) error {
	logger.Info("执行Cursor注册流程")

	// 根据配置选择执行模式
	if m.config.FlowConfig.CursorQuickMode {
		logger.Info("使用快速注册模式")
		sequence := m.flowFactory.CreateQuickRegistrationSequence(account, m.config.FlowConfig.CursorBaseURL)
		return sequence.Execute(m.ctx)
	} else if m.config.FlowConfig.CursorDebugMode {
		logger.Info("使用调试注册模式")
		sequence := m.flowFactory.CreateDebugRegistrationSequence(account, m.config.FlowConfig.CursorBaseURL)
		return sequence.Execute(m.ctx)
	} else {
		logger.Info("使用标准注册模式")
		sequence := m.flowFactory.CreateStandardRegistrationSequence(account, m.config.FlowConfig.CursorBaseURL)
		return sequence.Execute(m.ctx)
	}
}

// executeAugmentLoginFlow 执行Augment登录流程
func (m *Manager) executeAugmentLoginFlow(account *generator.AccountInfo) error {
	logger.Info("执行Augment登录流程")

	// 根据配置选择执行模式
	if m.config.FlowConfig.AugmentQuickMode {
		logger.Info("使用快速登录模式")
		sequence := m.flowFactory.CreateQuickLoginSequence(account.Email, account.Password, m.config.FlowConfig.AugmentBaseURL)
		return sequence.Execute(m.ctx)
	} else if m.config.FlowConfig.AugmentDebugMode {
		logger.Info("使用调试登录模式")
		sequence := m.flowFactory.CreateDebugLoginSequence(account.Email, account.Password, m.config.FlowConfig.AugmentBaseURL)
		return sequence.Execute(m.ctx)
	} else {
		logger.Info("使用标准登录模式")
		sequence := m.flowFactory.CreateStandardLoginSequence(account.Email, account.Password, m.config.FlowConfig.AugmentBaseURL)
		return sequence.Execute(m.ctx)
	}
}

// executeCustomFlow 执行自定义流程
func (m *Manager) executeCustomFlow(account *generator.AccountInfo) error {
	logger.Info("执行自定义流程")

	// 这里可以实现自定义的自动化流程
	// 例如：组合多个步骤、自定义操作序列等

	// 示例：简单的导航和截图流程
	sequence := action.NewActionSequence("CustomFlow").
		Add(action.NewLogAction("开始自定义流程")).
		Add(action.NewNavigateAction("https://example.com").
			WithScreenshot(true, "custom_page_loaded")).
		Add(action.NewDelayAction(3 * time.Second).
			WithDescription("等待页面稳定")).
		Add(action.NewScreenshotAction("custom_flow_completed")).
		Add(action.NewLogAction("自定义流程完成"))

	return sequence.Execute(m.ctx)
}

// GetSupportedFlows 获取支持的流程列表
func (m *Manager) GetSupportedFlows() []string {
	return []string{
		"cursor_register",
		"augment_login",
		"custom",
	}
}

// ValidateFlow 验证流程配置
func (m *Manager) ValidateFlow() error {
	supportedFlows := m.GetSupportedFlows()

	// 检查流程是否支持
	flowSupported := false
	for _, flow := range supportedFlows {
		if flow == m.config.AutomationFlow {
			flowSupported = true
			break
		}
	}

	if !flowSupported {
		return fmt.Errorf("不支持的自动化流程: %s，支持的流程: %v",
			m.config.AutomationFlow, supportedFlows)
	}

	// 验证流程特定配置
	switch m.config.AutomationFlow {
	case "cursor_register":
		if m.config.FlowConfig.CursorBaseURL == "" {
			return fmt.Errorf("Cursor注册流程需要配置CursorBaseURL")
		}
	case "augment_login":
		if m.config.FlowConfig.AugmentBaseURL == "" {
			return fmt.Errorf("Augment登录流程需要配置AugmentBaseURL")
		}
	}

	// 验证超时配置
	if err := m.timeoutManager.ValidateTimeouts(); err != nil {
		return fmt.Errorf("超时配置验证失败: %w", err)
	}

	// 记录超时配置
	m.timeoutManager.LogTimeoutConfig()

	return nil
}

// GetFlowDescription 获取流程描述
func (m *Manager) GetFlowDescription() string {
	switch m.config.AutomationFlow {
	case "cursor_register":
		mode := "标准模式"
		if m.config.FlowConfig.CursorQuickMode {
			mode = "快速模式"
		} else if m.config.FlowConfig.CursorDebugMode {
			mode = "调试模式"
		}
		return fmt.Sprintf("Cursor注册流程 (%s) - %s", mode, m.config.FlowConfig.CursorBaseURL)
	case "augment_login":
		mode := "标准模式"
		if m.config.FlowConfig.AugmentQuickMode {
			mode = "快速模式"
		} else if m.config.FlowConfig.AugmentDebugMode {
			mode = "调试模式"
		}
		return fmt.Sprintf("Augment登录流程 (%s) - %s", mode, m.config.FlowConfig.AugmentBaseURL)
	case "custom":
		return "自定义流程 - 可配置的自动化操作序列"
	default:
		return fmt.Sprintf("未知流程: %s", m.config.AutomationFlow)
	}
}

// SetRetryConfig 设置重试配置
func (m *Manager) SetRetryConfig(maxRetries int, retryDelay time.Duration) {
	// 这里可以设置全局的重试配置
	// 影响所有Action和Step的重试行为
	logger.Infof("设置重试配置: 最大重试次数=%d, 重试延迟=%v", maxRetries, retryDelay)
}

// EnableDebugMode 启用调试模式
func (m *Manager) EnableDebugMode() {
	logger.Info("启用自动化调试模式")
	// 可以设置全局调试标志，影响所有操作的调试行为
}

// GetContext 获取操作上下文
func (m *Manager) GetContext() *action.ActionContext {
	return m.ctx
}

// GetConfig 获取配置
func (m *Manager) GetConfig() *config.Config {
	return m.config
}

// GetTimeoutManager 获取超时管理器
func (m *Manager) GetTimeoutManager() *TimeoutManager {
	return m.timeoutManager
}

// GetFlowFactory 获取流程工厂
func (m *Manager) GetFlowFactory() *FlowFactory {
	return m.flowFactory
}
