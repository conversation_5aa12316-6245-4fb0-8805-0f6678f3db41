# 自动化操作模块

这个模块将原有的 `automation-aug.go` 中的各个操作步骤抽取为独立的、可重用的操作组件，提供了灵活的操作组装能力。

## 模块结构

```
browser/automation/action/
├── base.go        # 基础接口和工具
├── navigate.go    # 导航相关操作
├── input.go       # 输入相关操作
├── button.go      # 按钮点击操作
├── verify.go      # 验证相关操作
├── debug.go       # 调试和工具操作
├── sequence.go    # 操作序列管理
└── README.md      # 本文档
```

## 核心概念

### 1. Action 接口
所有操作都实现了 `Action` 接口：
```go
type Action interface {
    Execute(ctx *ActionContext) error
    GetName() string
}
```

### 2. ActionContext 操作上下文
包含执行操作所需的共享资源：
```go
type ActionContext struct {
    Manager *browser.Manager
}
```

### 3. ActionSequence 操作序列
用于组合多个操作：
```go
type ActionSequence struct {
    Name        string
    Actions     []Action
    StopOnError bool
    Timeout     time.Duration
}
```

## 可用操作类型

### 导航操作 (navigate.go)
- `NavigateAction` - 页面导航
- `WaitForPageLoadAction` - 等待页面加载
- `GetPageInfoAction` - 获取页面信息

### 输入操作 (input.go)
- `InputAction` - 通用输入操作
- `InputEmailAction` - 邮箱输入
- `InputPasswordAction` - 密码输入

### 按钮操作 (button.go)
- `ButtonClickAction` - 通用按钮点击
- `ContinueButtonAction` - 继续按钮点击
- `LoginButtonAction` - 登录按钮点击

### 验证操作 (verify.go)
- `VerifyLoginResultAction` - 验证登录结果
- `WaitForElementAction` - 等待元素出现

### 调试操作 (debug.go)
- `ScreenshotAction` - 截图
- `SavePageHTMLAction` - 保存页面HTML
- `DelayAction` - 延迟等待
- `LogAction` - 日志记录
- `ExecuteScriptAction` - 执行JavaScript

## 使用方法

### 1. 基本使用

```go
// 创建操作上下文
ctx := action.NewActionContext(browserManager)

// 创建并执行单个操作
navigateAction := action.NewNavigateAction("https://login.augmentcode.com/")
err := navigateAction.Execute(ctx)
```

### 2. 使用预定义序列

```go
// 快速登录序列
sequence := action.QuickLoginSequence(email, password)
err := sequence.Execute(ctx)

// 最小登录序列
sequence := action.MinimalLoginSequence(email, password)
err := sequence.Execute(ctx)

// 调试登录序列
sequence := action.DebugLoginSequence(email, password)
err := sequence.Execute(ctx)
```

### 3. 使用序列构建器

```go
sequence := action.NewAugmentLoginSequence().
    WithNavigate("https://login.augmentcode.com/").
    WithInputEmail(email).
    WithContinueButton().
    WithInputPassword(password).
    WithLoginButton().
    WithVerifyResult().
    GetSequence()

err := sequence.Execute(ctx)
```

### 4. 手动构建序列

```go
sequence := action.NewActionSequence("CustomLogin").
    Add(action.NewLogAction("开始登录")).
    Add(action.NewNavigateAction("https://login.augmentcode.com/")).
    Add(action.NewInputEmailAction(email)).
    Add(action.NewContinueButtonAction()).
    Add(action.NewDelayAction(2 * time.Second)).
    Add(action.NewInputPasswordAction(password)).
    Add(action.NewLoginButtonAction()).
    Add(action.NewVerifyLoginResultAction())

err := sequence.Execute(ctx)
```

### 5. 高级配置

```go
// 配置操作选项
emailAction := action.NewInputEmailAction(email).
    WithTimeout(10 * time.Second).
    WithScreenshot(true, "email_input").
    WithClearFirst(true)

// 配置序列选项
sequence := action.NewActionSequence("Login").
    WithStopOnError(false).  // 遇到错误继续执行
    WithTimeout(5 * time.Minute).  // 设置总超时
    Add(emailAction)
```

## 操作配置选项

### 导航操作
```go
action.NewNavigateAction(url).
    WithWaitTime(3 * time.Second).
    WithScreenshot(true, "page_loaded")
```

### 输入操作
```go
action.NewInputAction(selectors, value).
    WithClearFirst(true).
    WithClickFirst(true).
    WithTimeout(10 * time.Second).
    WithScreenshot(true, "input_done")
```

### 按钮操作
```go
action.NewButtonClickAction(selectors).
    WithJavaScriptFallback(true, []string{"login", "submit"}).
    WithTimeout(5 * time.Second).
    WithWaitAfterClick(2 * time.Second)
```

### 验证操作
```go
action.NewVerifyLoginResultAction().
    WithWaitTime(5 * time.Second).
    WithSuccessURLs([]string{"dashboard.com"}).
    WithRetries(3, 2 * time.Second).
    WithAllowUnknownStatus(true)
```

## 错误处理

### 序列级别错误处理
```go
sequence := action.NewActionSequence("Login").
    WithStopOnError(false)  // 遇到错误继续执行
```

### 操作级别错误处理
```go
action.NewScreenshotAction("debug").
    WithRequired(false)  // 失败时不中断序列
```

## 调试功能

### 详细日志
```go
sequence.Add(action.NewLogAction("开始登录流程").WithLevel("DEBUG"))
```

### 截图调试
```go
sequence.Add(action.NewScreenshotAction("step1_completed"))
```

### 保存页面HTML
```go
sequence.Add(action.NewSavePageHTMLAction("login_page"))
```

### 执行自定义脚本
```go
script := "return document.querySelector('input[name=\"email\"]').value;"
sequence.Add(action.NewExecuteScriptAction(script).
    WithDescription("获取邮箱输入框的值").
    WithStoreResult("email_value"))
```

## 扩展开发

### 创建自定义操作
```go
type CustomAction struct {
    // 自定义字段
}

func (a *CustomAction) Execute(ctx *action.ActionContext) error {
    // 实现操作逻辑
    return nil
}

func (a *CustomAction) GetName() string {
    return "CustomAction"
}
```

### 创建自定义序列
```go
func CustomLoginSequence(email, password string) *action.ActionSequence {
    return action.NewActionSequence("CustomLogin").
        Add(action.NewLogAction("开始自定义登录")).
        Add(action.NewNavigateAction("https://custom-login.com/")).
        // 添加更多操作...
}
```

## 最佳实践

1. **使用预定义序列**：对于常见场景，优先使用预定义的序列
2. **合理设置超时**：为操作和序列设置合适的超时时间
3. **添加调试信息**：在关键步骤添加日志和截图
4. **错误处理**：根据需要配置错误处理策略
5. **操作复用**：将常用的操作组合封装为可复用的序列
6. **渐进式开发**：从简单序列开始，逐步添加复杂功能

## 示例

完整的使用示例请参考：
- `examples/action-sequence-example.go` - 详细的使用演示
- `examples/augment-login-example.go` - 原有的登录示例

## 性能考虑

- 操作序列支持超时控制
- 可以配置是否在错误时停止
- 支持并发执行（需要注意浏览器上下文的线程安全）
- 内存使用优化（操作对象可重用）


# 改进思路

请按以下思路改进当前功能

- action 基础元素操作， 包括 input button navigation turnstile 认证处理等
- step 自动化常用的处理的步骤（调用 action）， 包括 fillEmail fillUsername fillPassword 等
- flow 具体的业务实现，不包括具体的元素处理逻辑，只定义处理逻辑流程和传递地址等（调用 step 或 action）， 如现在的 automation-aug.go 重命名为 augment, automation.go 重命名为 cursor.go