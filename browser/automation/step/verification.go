package step

import (
	"fmt"
	"strings"
	"time"

	"auto-register/browser/automation/action"
	"auto-register/logger"
)

// VerifyLoginResultStep 验证登录结果步骤
type VerifyLoginResultStep struct {
	WaitTime           time.Duration
	SuccessURLs        []string
	SuccessTitles      []string
	MaxRetries         int
	RetryInterval      time.Duration
	AllowUnknownStatus bool
	TakeScreenshot     bool
}

// NewVerifyLoginResultStep 创建验证登录结果步骤
func NewVerifyLoginResultStep() *VerifyLoginResultStep {
	return &VerifyLoginResultStep{
		WaitTime: 5 * time.Second,
		SuccessURLs: []string{
			"augmentcode.com",
			"dashboard",
			"home",
		},
		SuccessTitles: []string{
			"dashboard", "home", "welcome", "augment",
		},
		MaxRetries:         2,
		RetryInterval:      3 * time.Second,
		AllowUnknownStatus: true,
		TakeScreenshot:     true,
	}
}

// WithWaitTime 设置等待时间
func (s *VerifyLoginResultStep) WithWaitTime(duration time.Duration) *VerifyLoginResultStep {
	s.WaitTime = duration
	return s
}

// WithSuccessURLs 设置成功URL模式
func (s *VerifyLoginResultStep) WithSuccessURLs(urls []string) *VerifyLoginResultStep {
	s.SuccessURLs = urls
	return s
}

// WithSuccessTitles 设置成功标题模式
func (s *VerifyLoginResultStep) WithSuccessTitles(titles []string) *VerifyLoginResultStep {
	s.SuccessTitles = titles
	return s
}

// WithRetries 设置重试选项
func (s *VerifyLoginResultStep) WithRetries(maxRetries int, interval time.Duration) *VerifyLoginResultStep {
	s.MaxRetries = maxRetries
	s.RetryInterval = interval
	return s
}

// WithAllowUnknownStatus 设置是否允许未知状态
func (s *VerifyLoginResultStep) WithAllowUnknownStatus(allow bool) *VerifyLoginResultStep {
	s.AllowUnknownStatus = allow
	return s
}

// WithScreenshot 设置截图选项
func (s *VerifyLoginResultStep) WithScreenshot(take bool) *VerifyLoginResultStep {
	s.TakeScreenshot = take
	return s
}

// Execute 执行验证登录结果步骤
func (s *VerifyLoginResultStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行验证登录结果步骤")

	verifyAction := action.NewVerifyLoginResultAction().
		WithWaitTime(s.WaitTime).
		WithSuccessURLs(s.SuccessURLs).
		WithSuccessTitles(s.SuccessTitles).
		WithRetries(s.MaxRetries, s.RetryInterval).
		WithAllowUnknownStatus(s.AllowUnknownStatus)

	if err := verifyAction.Execute(ctx); err != nil {
		return fmt.Errorf("验证登录结果失败: %w", err)
	}

	logger.Info("登录结果验证完成")
	return nil
}

// GetName 获取步骤名称
func (s *VerifyLoginResultStep) GetName() string {
	return "VerifyLoginResult"
}

// HandleTurnstileStep 处理Turnstile验证步骤
type HandleTurnstileStep struct {
	WaitTimeout   time.Duration
	MaxRetries    int
	RetryInterval time.Duration
	Required      bool
}

// NewHandleTurnstileStep 创建处理Turnstile验证步骤
func NewHandleTurnstileStep() *HandleTurnstileStep {
	return &HandleTurnstileStep{
		WaitTimeout:   30 * time.Second,
		MaxRetries:    3,
		RetryInterval: 5 * time.Second,
		Required:      false,
	}
}

// WithTimeout 设置超时时间
func (s *HandleTurnstileStep) WithTimeout(timeout time.Duration) *HandleTurnstileStep {
	s.WaitTimeout = timeout
	return s
}

// WithRetries 设置重试选项
func (s *HandleTurnstileStep) WithRetries(maxRetries int, interval time.Duration) *HandleTurnstileStep {
	s.MaxRetries = maxRetries
	s.RetryInterval = interval
	return s
}

// WithRequired 设置是否必需
func (s *HandleTurnstileStep) WithRequired(required bool) *HandleTurnstileStep {
	s.Required = required
	return s
}

// Execute 执行处理Turnstile验证步骤
func (s *HandleTurnstileStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行处理Turnstile验证步骤")

	turnstileAction := action.NewTurnstileAction().
		WithTimeout(s.WaitTimeout).
		WithRetries(s.MaxRetries, s.RetryInterval)

	if err := turnstileAction.Execute(ctx); err != nil {
		if s.Required {
			return fmt.Errorf("处理Turnstile验证失败: %w", err)
		} else {
			logger.Warnf("Turnstile验证失败，但不是必需的: %v", err)
		}
	}

	logger.Info("Turnstile验证处理完成")
	return nil
}

// GetName 获取步骤名称
func (s *HandleTurnstileStep) GetName() string {
	return "HandleTurnstile"
}

// WaitForElementStep 等待元素出现步骤
type WaitForElementStep struct {
	Selectors   action.ElementSelectors
	Timeout     time.Duration
	Required    bool
	Description string
}

// NewWaitForElementStep 创建等待元素出现步骤
func NewWaitForElementStep(selectors action.ElementSelectors, description string) *WaitForElementStep {
	return &WaitForElementStep{
		Selectors:   selectors,
		Timeout:     10 * time.Second,
		Required:    true,
		Description: description,
	}
}

// WithTimeout 设置超时时间
func (s *WaitForElementStep) WithTimeout(timeout time.Duration) *WaitForElementStep {
	s.Timeout = timeout
	return s
}

// WithRequired 设置是否必需
func (s *WaitForElementStep) WithRequired(required bool) *WaitForElementStep {
	s.Required = required
	return s
}

// Execute 执行等待元素出现步骤
func (s *WaitForElementStep) Execute(ctx *action.ActionContext) error {
	logger.Infof("执行等待元素出现步骤: %s", s.Description)

	waitAction := action.NewWaitForElementAction(s.Selectors, s.Timeout).
		WithRequired(s.Required).
		WithDescription(s.Description)

	if err := waitAction.Execute(ctx); err != nil {
		return fmt.Errorf("等待元素出现失败: %w", err)
	}

	logger.Infof("元素出现等待完成: %s", s.Description)
	return nil
}

// GetName 获取步骤名称
func (s *WaitForElementStep) GetName() string {
	return "WaitForElement"
}

// VerifyPageTitleStep 验证页面标题步骤
type VerifyPageTitleStep struct {
	ExpectedTitles []string
	PartialMatch   bool
	CaseSensitive  bool
}

// NewVerifyPageTitleStep 创建验证页面标题步骤
func NewVerifyPageTitleStep(expectedTitles []string) *VerifyPageTitleStep {
	return &VerifyPageTitleStep{
		ExpectedTitles: expectedTitles,
		PartialMatch:   true,
		CaseSensitive:  false,
	}
}

// WithPartialMatch 设置是否部分匹配
func (s *VerifyPageTitleStep) WithPartialMatch(partial bool) *VerifyPageTitleStep {
	s.PartialMatch = partial
	return s
}

// WithCaseSensitive 设置是否区分大小写
func (s *VerifyPageTitleStep) WithCaseSensitive(sensitive bool) *VerifyPageTitleStep {
	s.CaseSensitive = sensitive
	return s
}

// Execute 执行验证页面标题步骤
func (s *VerifyPageTitleStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行验证页面标题步骤")

	// 获取当前页面标题
	script := `return document.title;`
	var currentTitle string
	if err := ctx.Manager.ExecuteScript(script, &currentTitle); err != nil {
		return fmt.Errorf("获取页面标题失败: %w", err)
	}

	logger.Infof("当前页面标题: %s", currentTitle)

	// 检查标题是否匹配
	for _, expectedTitle := range s.ExpectedTitles {
		if s.titleMatches(currentTitle, expectedTitle) {
			logger.Infof("页面标题验证成功: %s", expectedTitle)
			return nil
		}
	}

	return fmt.Errorf("页面标题验证失败，当前标题: %s，期望标题: %v", currentTitle, s.ExpectedTitles)
}

// titleMatches 检查标题是否匹配
func (s *VerifyPageTitleStep) titleMatches(current, expected string) bool {
	if !s.CaseSensitive {
		current = strings.ToLower(current)
		expected = strings.ToLower(expected)
	}

	if s.PartialMatch {
		return strings.Contains(current, expected)
	} else {
		return current == expected
	}
}

// GetName 获取步骤名称
func (s *VerifyPageTitleStep) GetName() string {
	return "VerifyPageTitle"
}
