package email

import (
	"fmt"
	"time"

	"auto-register/config"
	"auto-register/logger"
)

// Handler 邮箱处理器接口
type Handler interface {
	GetVerificationCode(maxRetries int, retryInterval time.Duration) (string, error)
}

// VerificationHandler 邮箱验证处理器
type VerificationHandler struct {
	config  *config.Config
	account string
	handler Hand<PERSON>
}

// NewVerificationHandler 创建邮箱验证处理器
func NewVerificationHandler(cfg *config.Config, account string) *VerificationHandler {
	vh := &VerificationHandler{
		config:  cfg,
		account: account,
	}

	// 根据配置选择处理器
	if cfg.IsIMAPMode() {
		vh.handler = NewIMAPHandler(cfg, account)
	} else {
		vh.handler = NewTempMailHandler(cfg, account)
	}

	return vh
}

// GetVerificationCode 获取验证码
func (vh *VerificationHandler) GetVerificationCode() (string, error) {
	maxRetries := vh.config.MaxRetries
	retryInterval := time.Duration(vh.config.RetryInterval) * time.Second

	logger.Infof("开始获取验证码，最大重试次数: %d", maxRetries)

	for attempt := 1; attempt <= maxRetries; attempt++ {
		logger.Infof("尝试获取验证码 (第 %d/%d 次)", attempt, maxRetries)

		code, err := vh.handler.GetVerificationCode(1, 0)
		if err == nil && code != "" {
			logger.Infof("成功获取验证码: %s", code)
			return code, nil
		}

		if attempt < maxRetries {
			logger.Warnf("获取验证码失败: %v，%v 后重试", err, retryInterval)
			time.Sleep(retryInterval)
		} else {
			logger.Errorf("获取验证码失败，已达最大重试次数: %v", err)
		}
	}

	return "", fmt.Errorf("经过 %d 次尝试后仍未获取到验证码", maxRetries)
}

// GetVerificationCodeWithTimeout 带超时的获取验证码
func (vh *VerificationHandler) GetVerificationCodeWithTimeout(timeout time.Duration) (string, error) {
	resultChan := make(chan string, 1)
	errorChan := make(chan error, 1)

	go func() {
		code, err := vh.GetVerificationCode()
		if err != nil {
			errorChan <- err
		} else {
			resultChan <- code
		}
	}()

	select {
	case code := <-resultChan:
		return code, nil
	case err := <-errorChan:
		return "", err
	case <-time.After(timeout):
		return "", fmt.Errorf("获取验证码超时")
	}
}
