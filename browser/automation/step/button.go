package step

import (
	"fmt"
	"time"

	"auto-register/browser/automation/action"
	"auto-register/logger"
)

// ClickContinueStep 点击继续按钮步骤
type ClickContinueStep struct {
	WaitAfterClick time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewClickContinueStep 创建点击继续按钮步骤
func NewClickContinueStep() *ClickContinueStep {
	return &ClickContinueStep{
		WaitAfterClick: 2 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "continue_clicked",
	}
}

// WithWaitAfterClick 设置点击后等待时间
func (s *ClickContinueStep) WithWaitAfterClick(duration time.Duration) *ClickContinueStep {
	s.WaitAfterClick = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *ClickContinueStep) WithScreenshot(take bool, name string) *ClickContinueStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行点击继续按钮步骤
func (s *ClickContinueStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行点击继续按钮步骤")

	buttonAction := action.NewButtonClickAction(action.ContinueButtonSelectors).
		WithTimeout(10 * time.Second).
		WithWaitAfterClick(s.WaitAfterClick).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := buttonAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击继续按钮失败: %w", err)
	}

	logger.Info("继续按钮点击完成")
	return nil
}

// GetName 获取步骤名称
func (s *ClickContinueStep) GetName() string {
	return "ClickContinue"
}

// ClickLoginStep 点击登录按钮步骤
type ClickLoginStep struct {
	WaitAfterClick time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewClickLoginStep 创建点击登录按钮步骤
func NewClickLoginStep() *ClickLoginStep {
	return &ClickLoginStep{
		WaitAfterClick: 3 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "login_clicked",
	}
}

// WithWaitAfterClick 设置点击后等待时间
func (s *ClickLoginStep) WithWaitAfterClick(duration time.Duration) *ClickLoginStep {
	s.WaitAfterClick = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *ClickLoginStep) WithScreenshot(take bool, name string) *ClickLoginStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行点击登录按钮步骤
func (s *ClickLoginStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行点击登录按钮步骤")

	buttonAction := action.NewButtonClickAction(action.LoginButtonSelectors).
		WithTimeout(10 * time.Second).
		WithWaitAfterClick(s.WaitAfterClick).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := buttonAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击登录按钮失败: %w", err)
	}

	logger.Info("登录按钮点击完成")
	return nil
}

// GetName 获取步骤名称
func (s *ClickLoginStep) GetName() string {
	return "ClickLogin"
}

// ClickSubmitStep 点击提交按钮步骤
type ClickSubmitStep struct {
	WaitAfterClick time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewClickSubmitStep 创建点击提交按钮步骤
func NewClickSubmitStep() *ClickSubmitStep {
	return &ClickSubmitStep{
		WaitAfterClick: 2 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "submit_clicked",
	}
}

// WithWaitAfterClick 设置点击后等待时间
func (s *ClickSubmitStep) WithWaitAfterClick(duration time.Duration) *ClickSubmitStep {
	s.WaitAfterClick = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *ClickSubmitStep) WithScreenshot(take bool, name string) *ClickSubmitStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行点击提交按钮步骤
func (s *ClickSubmitStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行点击提交按钮步骤")

	submitSelectors := action.ElementSelectors{
		`button[type="submit"]`,
		`input[type="submit"]`,
		`button:contains("Submit")`,
		`button:contains("提交")`,
		`button:contains("确认")`,
		`button:contains("Confirm")`,
		`[data-testid="submit-button"]`,
		`[data-testid="confirm-button"]`,
		`.submit-button`,
		`.confirm-button`,
	}

	buttonAction := action.NewButtonClickAction(submitSelectors).
		WithTimeout(10 * time.Second).
		WithWaitAfterClick(s.WaitAfterClick).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := buttonAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击提交按钮失败: %w", err)
	}

	logger.Info("提交按钮点击完成")
	return nil
}

// GetName 获取步骤名称
func (s *ClickSubmitStep) GetName() string {
	return "ClickSubmit"
}

// ClickRegisterStep 点击注册按钮步骤
type ClickRegisterStep struct {
	WaitAfterClick time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewClickRegisterStep 创建点击注册按钮步骤
func NewClickRegisterStep() *ClickRegisterStep {
	return &ClickRegisterStep{
		WaitAfterClick: 3 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "register_clicked",
	}
}

// WithWaitAfterClick 设置点击后等待时间
func (s *ClickRegisterStep) WithWaitAfterClick(duration time.Duration) *ClickRegisterStep {
	s.WaitAfterClick = duration
	return s
}

// WithScreenshot 设置截图选项
func (s *ClickRegisterStep) WithScreenshot(take bool, name string) *ClickRegisterStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行点击注册按钮步骤
func (s *ClickRegisterStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行点击注册按钮步骤")

	registerSelectors := action.ElementSelectors{
		`button[type="submit"]`,
		`input[type="submit"]`,
		`button:contains("Register")`,
		`button:contains("Sign up")`,
		`button:contains("Create")`,
		`button:contains("注册")`,
		`button:contains("创建")`,
		`[data-testid="register-button"]`,
		`[data-testid="signup-button"]`,
		`.register-button`,
		`.signup-button`,
	}

	buttonAction := action.NewButtonClickAction(registerSelectors).
		WithTimeout(10 * time.Second).
		WithWaitAfterClick(s.WaitAfterClick).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := buttonAction.Execute(ctx); err != nil {
		return fmt.Errorf("点击注册按钮失败: %w", err)
	}

	logger.Info("注册按钮点击完成")
	return nil
}

// GetName 获取步骤名称
func (s *ClickRegisterStep) GetName() string {
	return "ClickRegister"
}
