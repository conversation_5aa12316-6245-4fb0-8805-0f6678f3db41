package generator

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// AccountInfo 账号信息
type AccountInfo struct {
	Email     string `json:"email"`
	Password  string `json:"password"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// AccountGenerator 账号生成器
type AccountGenerator struct {
	nameGen *NameGenerator
	domain  string
	rand    *rand.Rand
}

// NewAccountGenerator 创建新的账号生成器
func NewAccountGenerator(domain string) *AccountGenerator {
	return &AccountGenerator{
		nameGen: NewNameGenerator(),
		domain:  domain,
		rand:    rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateAccount 生成完整的账号信息
func (ag *AccountGenerator) GenerateAccount() *AccountInfo {
	firstName := ag.nameGen.GenerateFirstName()
	lastName := ag.nameGen.GenerateLastName()
	email := ag.generateEmail(firstName)
	password := ag.generatePassword()

	return &AccountInfo{
		Email:     email,
		Password:  password,
		FirstName: firstName,
		LastName:  lastName,
	}
}

// generateEmail 生成邮箱地址
func (ag *AccountGenerator) generateEmail(firstName string) string {
	// 生成时间戳后缀
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	// 随机选择时间戳的长度 (0-4位)
	length := ag.rand.Intn(5)
	if length > 0 && length <= len(timestamp) {
		timestamp = timestamp[len(timestamp)-length:]
	} else {
		timestamp = ""
	}

	// 生成邮箱地址
	email := fmt.Sprintf("%s%s@%s", strings.ToLower(firstName), timestamp, ag.domain)
	return email
}

// generatePassword 生成随机密码
func (ag *AccountGenerator) generatePassword() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	const passwordLength = 12

	password := make([]byte, passwordLength)
	for i := range password {
		password[i] = charset[ag.rand.Intn(len(charset))]
	}

	return string(password)
}

// GenerateEmail 生成邮箱地址（公开方法）
func (ag *AccountGenerator) GenerateEmail() string {
	firstName := ag.nameGen.GenerateFirstName()
	return ag.generateEmail(firstName)
}

// GeneratePassword 生成密码（公开方法）
func (ag *AccountGenerator) GeneratePassword() string {
	return ag.generatePassword()
}

// GenerateFirstName 生成名字（公开方法）
func (ag *AccountGenerator) GenerateFirstName() string {
	return ag.nameGen.GenerateFirstName()
}

// GenerateLastName 生成姓氏（公开方法）
func (ag *AccountGenerator) GenerateLastName() string {
	return ag.nameGen.GenerateLastName()
}
