package email

import (
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"

	"auto-register/config"
	"auto-register/logger"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/client"
	"github.com/emersion/go-message/mail"
)

// IMAPHandler IMAP邮箱处理器
type IMAPHandler struct {
	config  *config.Config
	account string
}

// NewIMAPHandler 创建IMAP处理器
func NewIMAPHandler(cfg *config.Config, account string) *IMAPHandler {
	return &IMAPHandler{
		config:  cfg,
		account: account,
	}
}

// GetVerificationCode 获取验证码
func (ih *IMAPHandler) GetVerificationCode(maxRetries int, retryInterval time.Duration) (string, error) {
	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			time.Sleep(retryInterval)
		}

		code, err := ih.fetchVerificationCode()
		if err == nil && code != "" {
			return code, nil
		}

		logger.Debugf("IMAP获取验证码失败 (尝试 %d/%d): %v", attempt+1, maxRetries, err)
	}

	return "", fmt.Errorf("IMAP获取验证码失败，已达最大重试次数")
}

// fetchVerificationCode 从IMAP服务器获取验证码
func (ih *IMAPHandler) fetchVerificationCode() (string, error) {
	// 连接到IMAP服务器
	c, err := client.DialTLS(fmt.Sprintf("%s:%d", ih.config.IMAPServer, ih.config.IMAPPort), nil)
	if err != nil {
		return "", fmt.Errorf("连接IMAP服务器失败: %w", err)
	}
	defer c.Close()

	// 登录
	if err := c.Login(ih.config.IMAPUser, ih.config.IMAPPass); err != nil {
		return "", fmt.Errorf("IMAP登录失败: %w", err)
	}
	defer c.Logout()

	// 处理网易系邮箱的特殊要求
	if ih.isNetEaseEmail() {
		if err := ih.handleNetEaseID(c); err != nil {
			logger.Warnf("处理网易邮箱ID失败: %v", err)
		}
	}

	// 选择邮箱目录
	_, err = c.Select(ih.config.IMAPDir, false)
	if err != nil {
		return "", fmt.Errorf("选择邮箱目录失败: %w", err)
	}

	// 搜索邮件
	var criteria *imap.SearchCriteria
	if ih.isNetEaseEmail() {
		// 网易邮箱按日期搜索未读邮件
		criteria = &imap.SearchCriteria{
			Since: time.Now().Truncate(24 * time.Hour),
		}
	} else {
		// 其他邮箱按收件人搜索
		criteria = &imap.SearchCriteria{
			Header: map[string][]string{
				"To": {ih.account},
			},
		}
	}

	uids, err := c.Search(criteria)
	if err != nil {
		return "", fmt.Errorf("搜索邮件失败: %w", err)
	}

	if len(uids) == 0 {
		return "", fmt.Errorf("未找到相关邮件")
	}

	// 获取最新的邮件
	seqset := new(imap.SeqSet)
	seqset.AddNum(uids[len(uids)-1])

	messages := make(chan *imap.Message, 1)
	done := make(chan error, 1)
	go func() {
		done <- c.Fetch(seqset, []imap.FetchItem{imap.FetchEnvelope, imap.FetchBody}, messages)
	}()

	msg := <-messages
	if err := <-done; err != nil {
		return "", fmt.Errorf("获取邮件失败: %w", err)
	}

	// 检查发件人（网易邮箱需要额外验证）
	if ih.isNetEaseEmail() {
		if !strings.Contains(msg.Envelope.From[0].Address(), ih.account) {
			return "", fmt.Errorf("邮件收件人不匹配")
		}
	}

	// 解析邮件内容
	body, err := ih.parseEmailBody(msg)
	if err != nil {
		return "", fmt.Errorf("解析邮件内容失败: %w", err)
	}

	// 提取验证码
	code := ih.extractVerificationCode(body)
	if code == "" {
		return "", fmt.Errorf("未找到验证码")
	}

	// 删除已处理的邮件
	if err := ih.deleteMessage(c, seqset); err != nil {
		logger.Warnf("删除邮件失败: %v", err)
	}

	return code, nil
}

// isNetEaseEmail 检查是否为网易系邮箱
func (ih *IMAPHandler) isNetEaseEmail() bool {
	netEaseDomains := []string{"@163.com", "@126.com", "@yeah.net"}
	for _, domain := range netEaseDomains {
		if strings.HasSuffix(ih.config.IMAPUser, domain) {
			return true
		}
	}
	return false
}

// handleNetEaseID 处理网易邮箱的ID验证
func (ih *IMAPHandler) handleNetEaseID(c *client.Client) error {
	// 网易邮箱的ID验证在go-imap中不直接支持
	// 这里暂时跳过，如果需要可以使用原始IMAP命令
	logger.Debug("跳过网易邮箱ID验证")
	return nil
}

// parseEmailBody 解析邮件正文
func (ih *IMAPHandler) parseEmailBody(msg *imap.Message) (string, error) {
	var body string

	for _, part := range msg.Body {
		if part == nil {
			continue
		}

		mr, err := mail.CreateReader(part)
		if err != nil {
			continue
		}

		for {
			p, err := mr.NextPart()
			if err == io.EOF {
				break
			}
			if err != nil {
				continue
			}

			switch h := p.Header.(type) {
			case *mail.InlineHeader:
				if strings.HasPrefix(h.Get("Content-Type"), "text/plain") {
					b, err := io.ReadAll(p.Body)
					if err != nil {
						continue
					}
					body += string(b)
				}
			}
		}
	}

	return body, nil
}

// extractVerificationCode 提取验证码
func (ih *IMAPHandler) extractVerificationCode(body string) string {
	// 移除邮箱地址，避免域名中的数字被误识别
	body = strings.ReplaceAll(body, ih.account, "")

	// 查找6位数字验证码
	re := regexp.MustCompile(`\b\d{6}\b`)
	matches := re.FindAllString(body, -1)

	if len(matches) > 0 {
		return matches[0]
	}

	return ""
}

// deleteMessage 删除邮件
func (ih *IMAPHandler) deleteMessage(c *client.Client, seqset *imap.SeqSet) error {
	// 标记为删除
	item := imap.FormatFlagsOp(imap.AddFlags, true)
	flags := []interface{}{imap.DeletedFlag}
	if err := c.Store(seqset, item, flags, nil); err != nil {
		return err
	}

	// 执行删除
	return c.Expunge(nil)
}
