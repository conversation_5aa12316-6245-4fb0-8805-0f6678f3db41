package generator

import (
	"strings"
	"testing"
)

func TestAccountGenerator(t *testing.T) {
	domain := "example.com"
	generator := NewAccountGenerator(domain)

	t.Run("GenerateAccount", func(t *testing.T) {
		account := generator.GenerateAccount()

		// 检查邮箱格式
		if !strings.Contains(account.Email, "@") {
			t.<PERSON><PERSON>("邮箱格式错误: %s", account.Email)
		}

		if !strings.HasSuffix(account.Email, "@"+domain) {
			t.<PERSON>("邮箱域名错误: %s", account.Email)
		}

		// 检查密码长度
		if len(account.Password) != 12 {
			t.<PERSON><PERSON><PERSON>("密码长度错误，期望12位，实际%d位: %s", len(account.Password), account.Password)
		}

		// 检查姓名不为空
		if account.FirstName == "" {
			t.<PERSON><PERSON>r("名字不能为空")
		}

		if account.LastName == "" {
			t.<PERSON><PERSON>("姓氏不能为空")
		}

		t.<PERSON>gf("生成的账号信息:")
		t.Logf("  邮箱: %s", account.Email)
		t.Logf("  密码: %s", account.Password)
		t.Logf("  姓名: %s %s", account.FirstName, account.LastName)
	})

	t.Run("GenerateMultipleAccounts", func(t *testing.T) {
		accounts := make([]*AccountInfo, 5)
		for i := 0; i < 5; i++ {
			accounts[i] = generator.GenerateAccount()
		}

		// 检查生成的账号是否不同
		for i := 0; i < len(accounts); i++ {
			for j := i + 1; j < len(accounts); j++ {
				if accounts[i].Email == accounts[j].Email {
					t.Errorf("生成了重复的邮箱: %s", accounts[i].Email)
				}
				if accounts[i].Password == accounts[j].Password {
					t.Errorf("生成了重复的密码: %s", accounts[i].Password)
				}
			}
		}
	})
}

func TestNameGenerator(t *testing.T) {
	generator := NewNameGenerator()

	t.Run("GenerateName", func(t *testing.T) {
		name := generator.GenerateName()
		if name == "" {
			t.Error("生成的姓名不能为空")
		}
		t.Logf("生成的姓名: %s", name)
	})

	t.Run("GenerateMultipleNames", func(t *testing.T) {
		names := make([]string, 10)
		for i := 0; i < 10; i++ {
			names[i] = generator.GenerateName()
		}

		// 检查是否有变化（虽然可能重复，但应该有一定的随机性）
		allSame := true
		for i := 1; i < len(names); i++ {
			if names[i] != names[0] {
				allSame = false
				break
			}
		}

		if allSame {
			t.Error("生成的姓名完全相同，缺乏随机性")
		}

		t.Logf("生成的姓名列表: %v", names)
	})
}

func BenchmarkAccountGeneration(b *testing.B) {
	generator := NewAccountGenerator("example.com")
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = generator.GenerateAccount()
	}
}

func BenchmarkNameGeneration(b *testing.B) {
	generator := NewNameGenerator()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = generator.GenerateName()
	}
}
