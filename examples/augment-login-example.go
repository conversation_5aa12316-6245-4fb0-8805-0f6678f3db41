package main

import (
	"fmt"
	"log"
	"os"

	"auto-register/browser"
	"auto-register/config"
	"auto-register/logger"
)

func main() {
	// 初始化日志
	if err := logger.InitLogger("INFO", ""); err != nil {
		log.Fatalf("日志初始化失败: %v", err)
	}

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 从环境变量或命令行参数获取登录凭据
	email := os.Getenv("AUGMENT_EMAIL")
	password := os.Getenv("AUGMENT_PASSWORD")

	if email == "" || password == "" {
		fmt.Println("使用方法:")
		fmt.Println("export AUGMENT_EMAIL=<EMAIL>")
		fmt.Println("export AUGMENT_PASSWORD=your-password")
		fmt.Println("go run examples/augment-login-example.go")
		os.Exit(1)
	}

	// 初始化浏览器管理器
	browserManager := browser.NewManager(cfg)
	if err := browserManager.Start(); err != nil {
		log.Fatalf("启动浏览器失败: %v", err)
	}
	defer browserManager.Stop()

	// 创建Augment登录自动化实例
	augmentLogin := browser.NewAugmentLoginAutomation(browserManager)

	// 执行登录
	logger.Info("开始Augment登录流程...")
	if err := augmentLogin.LoginAccount(email, password); err != nil {
		log.Fatalf("登录失败: %v", err)
	}

	// 获取当前页面信息
	pageInfo, err := augmentLogin.GetCurrentPageInfo()
	if err != nil {
		logger.Warnf("获取页面信息失败: %v", err)
	} else {
		logger.Info("登录后页面信息:")
		for key, value := range pageInfo {
			logger.Infof("  %s: %s", key, value)
		}
	}

	logger.Info("登录流程完成!")
	fmt.Println("\n按回车键退出...")
	fmt.Scanln()
}
