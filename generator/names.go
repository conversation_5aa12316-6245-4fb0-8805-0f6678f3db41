package generator

import (
	"bufio"
	"math/rand"
	"os"
	"strings"
	"time"
)

// 默认姓名列表，当文件不存在时使用
var defaultNames = []string{
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "Aaliyah", "Noah", "Kennedy", "Alan", "Kinsley", "Carl", "Allison",
	"Wayne", "Maya", "Arthur", "Sarah", "Gerald", "Madelyn", "Harold", "Adeline",
	"Jordan", "Alexa", "Jesse", "Ariana", "Bryan", "Elena", "Lawrence", "Gabriella",
	"Arthur", "Alice", "Roger", "Madeline", "Albert", "Annie", "Joe", "Helen",
	"Juan", "Maria", "Wayne", "Diana", "Ralph", "Abigail", "Roy", "Julie",
	"Eugene", "Joyce", "Louis", "Virginia", "Philip", "Victoria", "Bobby", "Kelly",
}

// NameGenerator 姓名生成器
type NameGenerator struct {
	names []string
	rand  *rand.Rand
}

// NewNameGenerator 创建新的姓名生成器
func NewNameGenerator() *NameGenerator {
	ng := &NameGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	// 尝试从文件加载姓名
	if names, err := ng.loadNamesFromFile("names-dataset.txt"); err == nil {
		ng.names = names
	} else {
		// 使用默认姓名列表
		ng.names = defaultNames
	}

	return ng
}

// loadNamesFromFile 从文件加载姓名列表
func (ng *NameGenerator) loadNamesFromFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var names []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		name := strings.TrimSpace(scanner.Text())
		if name != "" {
			names = append(names, name)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return names, nil
}

// GenerateName 生成随机姓名
func (ng *NameGenerator) GenerateName() string {
	if len(ng.names) == 0 {
		return "User"
	}
	return ng.names[ng.rand.Intn(len(ng.names))]
}

// GenerateFirstName 生成名字
func (ng *NameGenerator) GenerateFirstName() string {
	return ng.GenerateName()
}

// GenerateLastName 生成姓氏
func (ng *NameGenerator) GenerateLastName() string {
	return ng.GenerateName()
}
