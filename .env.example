# Auto Register Framework 配置示例

# ===== 自动化流程配置 =====
# 可选值: cursor_register, augment_login, custom
AUTOMATION_FLOW=cursor_register

# ===== 域名配置 =====
DOMAIN=example.com

# ===== 邮箱配置 =====
# 临时邮箱配置
TEMP_MAIL=testuser
TEMP_MAIL_EPIN=123456
TEMP_MAIL_EXT=@mailto.plus

# IMAP邮箱配置 (当TEMP_MAIL为空时使用)
IMAP_SERVER=imap.xxxxx.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=xxxxxxxxxxxxx
IMAP_DIR=inbox
IMAP_PROTOCOL=IMAP

# ===== 浏览器配置 =====
BROWSER_HEADLESS=false
BROWSER_PATH=
BROWSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.92 Safari/537.36
BROWSER_PROXY=

# ===== 日志配置 =====
LOG_LEVEL=debug
LOG_FILE=auto-register.log

# ===== 注册配置 =====
MAX_RETRIES=3
RETRY_INTERVAL=60
VERIFICATION_TIMEOUT=300

# ===== 流程特定配置 =====

# Cursor注册流程配置
FLOW_CURSOR_BASE_URL=https://cursor.sh/sign-up
FLOW_CURSOR_DEBUG_MODE=false
FLOW_CURSOR_QUICK_MODE=false

# Augment登录流程配置
FLOW_AUGMENT_BASE_URL=https://login.augmentcode.com/
FLOW_AUGMENT_DEBUG_MODE=false
FLOW_AUGMENT_QUICK_MODE=false

# 通用流程配置
FLOW_ENABLE_SCREENSHOTS=true
FLOW_SCREENSHOT_DIR=screenshots
FLOW_MAX_RETRIES=3
FLOW_RETRY_DELAY=5

# ===== 超时配置 =====
# 所有超时时间单位为秒，可根据网络环境和页面复杂度调整

# 元素等待超时 - 等待页面元素出现的最长时间
FLOW_ELEMENT_WAIT_TIMEOUT=10

# 页面加载超时 - 等待页面完全加载的最长时间
FLOW_PAGE_LOAD_TIMEOUT=30

# Turnstile验证超时 - 等待Turnstile验证完成的最长时间
FLOW_TURNSTILE_TIMEOUT=30

# 输入操作超时 - 输入框操作的最长等待时间
FLOW_INPUT_TIMEOUT=5

# 按钮操作超时 - 按钮点击操作的最长等待时间
FLOW_BUTTON_TIMEOUT=5

# 导航超时 - 页面导航的最长等待时间
FLOW_NAVIGATION_TIMEOUT=30
