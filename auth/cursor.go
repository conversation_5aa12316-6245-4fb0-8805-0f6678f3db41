package auth

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"auto-register/logger"

	_ "modernc.org/sqlite"
)

// CursorAuthManager Cursor认证管理器
type CursorAuthManager struct {
	dbPath string
}

// NewCursorAuthManager 创建Cursor认证管理器
func NewCursorAuthManager() (*CursorAuthManager, error) {
	dbPath, err := getCursorDBPath()
	if err != nil {
		return nil, fmt.Errorf("获取Cursor数据库路径失败: %w", err)
	}

	return &CursorAuthManager{
		dbPath: dbPath,
	}, nil
}

// getCursorDBPath 获取Cursor数据库路径
func getCursorDBPath() (string, error) {
	var dbPath string

	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		if appData == "" {
			return "", fmt.Errorf("APPDATA 环境变量未设置")
		}
		dbPath = filepath.Join(appData, "Cursor", "User", "globalStorage", "state.vscdb")

	case "darwin":
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", fmt.Errorf("获取用户主目录失败: %w", err)
		}
		dbPath = filepath.Join(homeDir, "Library", "Application Support", "Cursor", "User", "globalStorage", "state.vscdb")

	case "linux":
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", fmt.Errorf("获取用户主目录失败: %w", err)
		}
		dbPath = filepath.Join(homeDir, ".config", "Cursor", "User", "globalStorage", "state.vscdb")

	default:
		return "", fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}

	// 检查数据库文件是否存在
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		return "", fmt.Errorf("Cursor数据库文件不存在: %s", dbPath)
	}

	return dbPath, nil
}

// UpdateAuth 更新Cursor认证信息
func (cam *CursorAuthManager) UpdateAuth(email, accessToken, refreshToken string) error {
	logger.Info("更新Cursor认证信息")

	db, err := sql.Open("sqlite", cam.dbPath)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %w", err)
	}
	defer db.Close()

	// 准备更新的键值对
	updates := map[string]string{
		"cursorAuth/cachedSignUpType": "Auth_0",
	}

	if email != "" {
		updates["cursorAuth/cachedEmail"] = email
	}
	if accessToken != "" {
		updates["cursorAuth/accessToken"] = accessToken
	}
	if refreshToken != "" {
		updates["cursorAuth/refreshToken"] = refreshToken
	}

	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 更新每个键值对
	for key, value := range updates {
		if err := cam.upsertKeyValue(tx, key, value); err != nil {
			return fmt.Errorf("更新 %s 失败: %w", key, err)
		}
		logger.Infof("成功更新 %s", getKeyDisplayName(key))
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	logger.Info("Cursor认证信息更新完成")
	return nil
}

// upsertKeyValue 插入或更新键值对
func (cam *CursorAuthManager) upsertKeyValue(tx *sql.Tx, key, value string) error {
	// 检查键是否存在
	var count int
	err := tx.QueryRow("SELECT COUNT(*) FROM itemTable WHERE key = ?", key).Scan(&count)
	if err != nil {
		return fmt.Errorf("查询键失败: %w", err)
	}

	if count == 0 {
		// 插入新记录
		_, err = tx.Exec("INSERT INTO itemTable (key, value) VALUES (?, ?)", key, value)
		if err != nil {
			return fmt.Errorf("插入记录失败: %w", err)
		}
	} else {
		// 更新现有记录
		result, err := tx.Exec("UPDATE itemTable SET value = ? WHERE key = ?", value, key)
		if err != nil {
			return fmt.Errorf("更新记录失败: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("获取影响行数失败: %w", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("未更新任何记录")
		}
	}

	return nil
}

// getKeyDisplayName 获取键的显示名称
func getKeyDisplayName(key string) string {
	switch key {
	case "cursorAuth/cachedSignUpType":
		return "登录状态"
	case "cursorAuth/cachedEmail":
		return "邮箱地址"
	case "cursorAuth/accessToken":
		return "访问令牌"
	case "cursorAuth/refreshToken":
		return "刷新令牌"
	default:
		return key
	}
}

// GetAuthInfo 获取当前认证信息
func (cam *CursorAuthManager) GetAuthInfo() (map[string]string, error) {
	db, err := sql.Open("sqlite", cam.dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}
	defer db.Close()

	authKeys := []string{
		"cursorAuth/cachedSignUpType",
		"cursorAuth/cachedEmail",
		"cursorAuth/accessToken",
		"cursorAuth/refreshToken",
	}

	authInfo := make(map[string]string)

	for _, key := range authKeys {
		var value string
		err := db.QueryRow("SELECT value FROM itemTable WHERE key = ?", key).Scan(&value)
		if err != nil && err != sql.ErrNoRows {
			return nil, fmt.Errorf("查询 %s 失败: %w", key, err)
		}
		authInfo[key] = value
	}

	return authInfo, nil
}

// ClearAuth 清除认证信息
func (cam *CursorAuthManager) ClearAuth() error {
	logger.Info("清除Cursor认证信息")

	db, err := sql.Open("sqlite", cam.dbPath)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %w", err)
	}
	defer db.Close()

	authKeys := []string{
		"cursorAuth/cachedSignUpType",
		"cursorAuth/cachedEmail",
		"cursorAuth/accessToken",
		"cursorAuth/refreshToken",
	}

	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	for _, key := range authKeys {
		_, err := tx.Exec("DELETE FROM itemTable WHERE key = ?", key)
		if err != nil {
			return fmt.Errorf("删除 %s 失败: %w", key, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	logger.Info("Cursor认证信息清除完成")
	return nil
}
