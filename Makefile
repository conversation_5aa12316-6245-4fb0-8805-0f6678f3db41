# Auto Register Framework Makefile

.PHONY: build run clean test deps help

# 默认目标
all: build

# 构建项目
build:
	@echo "构建 Auto Register Framework..."
	go build -o bin/auto-register main.go
	@echo "构建完成: bin/auto-register"

# 运行项目
run:
	@echo "运行 Auto Register Framework..."
	go run main.go

# 安装依赖
deps:
	@echo "安装依赖..."
	go mod tidy
	go mod download

# 清理构建文件
clean:
	@echo "清理构建文件..."
	rm -rf bin/
	rm -rf screenshots/
	rm -f *.log

# 运行测试
test:
	@echo "运行测试..."
	go test -v ./...

# 测试Turnstile验证码
test-turnstile:
	@echo "测试Turnstile验证码..."
	./scripts/test-turnstile.sh

# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 代码检查
lint:
	@echo "代码检查..."
	golangci-lint run

# 创建发布版本
release: clean deps
	@echo "创建发布版本..."
	mkdir -p bin
	
	# Windows
	GOOS=windows GOARCH=amd64 go build -o bin/auto-register-windows-amd64.exe main.go
	
	# Linux
	GOOS=linux GOARCH=amd64 go build -o bin/auto-register-linux-amd64 main.go
	
	# macOS
	GOOS=darwin GOARCH=amd64 go build -o bin/auto-register-darwin-amd64 main.go
	GOOS=darwin GOARCH=arm64 go build -o bin/auto-register-darwin-arm64 main.go
	
	@echo "发布版本创建完成"

# 初始化项目
init:
	@echo "初始化项目..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "已创建 .env 文件，请根据需要修改配置"; \
	else \
		echo ".env 文件已存在"; \
	fi
	@mkdir -p screenshots
	@mkdir -p logs

# 帮助信息
help:
	@echo "Auto Register Framework - 可用命令:"
	@echo ""
	@echo "  build          - 构建项目"
	@echo "  run            - 运行项目"
	@echo "  deps           - 安装依赖"
	@echo "  clean          - 清理构建文件"
	@echo "  test           - 运行测试"
	@echo "  test-turnstile - 测试Turnstile验证码"
	@echo "  fmt            - 格式化代码"
	@echo "  lint           - 代码检查"
	@echo "  release        - 创建发布版本"
	@echo "  init           - 初始化项目"
	@echo "  help           - 显示帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make init          # 初始化项目"
	@echo "  make deps          # 安装依赖"
	@echo "  make run           # 运行项目"
	@echo "  make build         # 构建项目"
	@echo "  make test-turnstile # 测试验证码处理"
