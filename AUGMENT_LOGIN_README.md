# Augment Code 登录自动化脚本

这是一个专门为 Augment Code 登录页面设计的自动化脚本，基于现有的自动注册框架构建。

## 功能特性

- ✅ **简化设计**: 只保留登录相关的必要功能，移除了复杂的注册逻辑
- ✅ **Auth0 兼容**: 专门适配 Auth0 登录流程的两步验证（邮箱 → 密码）
- ✅ **智能元素识别**: 使用多种选择器策略自动识别登录表单元素
- ✅ **错误处理**: 完善的错误检测和截图调试功能
- ✅ **页面状态检测**: 智能判断登录成功状态
- ✅ **人性化操作**: 模拟真实用户操作，包含随机延迟

## 文件结构

```
browser/
├── automation-aug.go      # Augment登录自动化脚本
└── ...                   # 其他现有文件

examples/
└── augment-login-example.go  # 使用示例
```

## 核心方法

### AugmentLoginAutomation 结构体

```go
type AugmentLoginAutomation struct {
    manager *Manager
}
```

### 主要方法

1. **LoginAccount(email, password string) error**
   - 主要的登录方法
   - 执行完整的登录流程

2. **LoginWithAccount(account *generator.AccountInfo) error**
   - 使用账号信息结构体登录
   - 便于与现有账号生成器集成

3. **GetCurrentPageInfo() (map[string]string, error)**
   - 获取当前页面信息（URL、标题等）
   - 用于调试和状态检查

4. **SavePageHTML(filename string) error**
   - 保存当前页面HTML用于调试
   - 自动创建debug目录

## 使用方法

### 1. 基本使用

```go
package main

import (
    "auto-register/browser"
    "auto-register/config"
    "auto-register/logger"
)

func main() {
    // 初始化配置和日志
    cfg, _ := config.LoadConfig()
    logger.InitLogger("INFO", "")
    
    // 启动浏览器
    browserManager := browser.NewManager(cfg)
    browserManager.Start()
    defer browserManager.Stop()
    
    // 创建登录自动化实例
    augmentLogin := browser.NewAugmentLoginAutomation(browserManager)
    
    // 执行登录
    err := augmentLogin.LoginAccount("<EMAIL>", "your-password")
    if err != nil {
        log.Fatal(err)
    }
}
```

### 2. 运行示例

```bash
# 设置环境变量
export AUGMENT_EMAIL=<EMAIL>
export AUGMENT_PASSWORD=your-password

# 运行示例
go run examples/augment-login-example.go
```

### 3. 配置环境

确保 `.env` 文件包含必要的浏览器配置：

```env
# 浏览器配置
BROWSER_HEADLESS=false          # 设为false可以看到浏览器操作
BROWSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
LOG_LEVEL=INFO
```

## 技术实现

### 登录流程

1. **导航到登录页面**: `https://login.augmentcode.com/`
2. **输入邮箱标识符**: 自动识别邮箱输入框并填写
3. **点击继续按钮**: 进入密码输入页面
4. **输入密码**: 自动识别密码输入框并填写
5. **点击登录按钮**: 提交登录表单
6. **验证登录结果**: 检查页面跳转和错误信息

### 元素识别策略

脚本使用多种选择器策略来识别页面元素：

- CSS选择器（name、id、type、data-testid等）
- 占位符文本匹配
- JavaScript动态查找
- 文本内容匹配

### 错误处理

- 自动截图保存到 `screenshots/` 目录
- 页面HTML保存到 `debug/` 目录
- 详细的日志记录
- 智能重试机制

## 调试功能

### 截图功能

脚本会在关键步骤自动截图：
- `login_page_loaded.png` - 登录页面加载完成
- `password_page_loaded.png` - 密码页面加载完成
- `login_submitted.png` - 登录提交后
- `login_success.png` - 登录成功
- `login_error.png` - 登录错误

### HTML保存

可以调用 `SavePageHTML()` 方法保存当前页面HTML用于调试。

## 注意事项

1. **合规使用**: 请确保在合法合规的前提下使用本工具
2. **频率控制**: 避免过于频繁的登录操作
3. **凭据安全**: 妥善保管登录凭据，不要硬编码在代码中
4. **网络环境**: 确保网络连接稳定
5. **浏览器版本**: 确保Chrome/Chromium版本与chromedp兼容

## 故障排除

### 常见问题

1. **找不到输入框**
   - 检查页面是否完全加载
   - 查看截图确认页面状态
   - 检查网络连接

2. **登录失败**
   - 验证邮箱和密码是否正确
   - 检查是否有验证码或其他安全措施
   - 查看错误截图和日志

3. **页面加载超时**
   - 检查网络连接
   - 增加等待时间
   - 考虑使用代理

## 扩展开发

如需添加新功能或适配其他登录页面，可以参考现有代码结构：

1. 在 `AugmentLoginAutomation` 结构体中添加新方法
2. 使用相同的元素识别策略
3. 添加适当的错误处理和日志记录
4. 包含截图和调试功能

## 许可证

本项目遵循与主项目相同的许可证。
