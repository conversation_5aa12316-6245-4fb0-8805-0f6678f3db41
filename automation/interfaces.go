package automation

import "time"

// BrowserManager 浏览器管理器接口
type BrowserManager interface {
	Navigate(url string) error
	Click(selector string) error
	SendKeys(selector, text string) error
	WaitForElement(selector string, timeout time.Duration) error
	IsElementPresent(selector string) bool
	ExecuteScript(script string, result interface{}) error
	TakeScreenshot(filename string) error
}

// ActionContext 操作上下文
type ActionContext struct {
	Manager BrowserManager
}

// NewActionContext 创建新的操作上下文
func NewActionContext(manager BrowserManager) *ActionContext {
	return &ActionContext{
		Manager: manager,
	}
}

// Action 基础操作接口
type Action interface {
	Execute(ctx *ActionContext) error
	GetName() string
}
