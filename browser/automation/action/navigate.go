package action

import (
	"fmt"
	"time"

	"auto-register/logger"
)

// NavigateAction 导航操作
type NavigateAction struct {
	URL         string
	WaitTime    time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewNavigateAction 创建导航操作
func NewNavigateAction(url string) *NavigateAction {
	return &NavigateAction{
		URL:            url,
		WaitTime:       2 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "page_loaded",
	}
}

// WithWaitTime 设置等待时间
func (a *NavigateAction) WithWaitTime(duration time.Duration) *NavigateAction {
	a.WaitTime = duration
	return a
}

// WithScreenshot 设置截图选项
func (a *NavigateAction) WithScreenshot(take bool, name string) *NavigateAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// Execute 执行导航操作
func (a *NavigateAction) Execute(ctx *ActionContext) error {
	logger.Infof("导航到页面: %s", a.URL)

	// 导航到指定URL
	if err := ctx.Manager.Navigate(a.URL); err != nil {
		return fmt.Errorf("导航到页面失败: %w", err)
	}

	// 等待页面加载
	if a.WaitTime > 0 {
		logger.Debugf("等待页面加载: %v", a.WaitTime)
		time.Sleep(a.WaitTime)
	}

	// 截图
	if a.TakeScreenshot {
		if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
			logger.Warnf("截图失败: %v", err)
		}
	}

	logger.Info("页面导航完成")
	return nil
}

// GetName 获取操作名称
func (a *NavigateAction) GetName() string {
	return "Navigate"
}

// WaitForPageLoadAction 等待页面加载完成操作
type WaitForPageLoadAction struct {
	Timeout time.Duration
}

// NewWaitForPageLoadAction 创建等待页面加载操作
func NewWaitForPageLoadAction(timeout time.Duration) *WaitForPageLoadAction {
	return &WaitForPageLoadAction{
		Timeout: timeout,
	}
}

// Execute 执行等待页面加载操作
func (a *WaitForPageLoadAction) Execute(ctx *ActionContext) error {
	logger.Debug("等待页面加载完成")

	script := `
		return document.readyState === 'complete' &&
			   (!window.jQuery || window.jQuery.active === 0);
	`

	start := time.Now()
	for time.Since(start) < a.Timeout {
		var ready bool
		if err := ctx.Manager.ExecuteScript(script, &ready); err == nil && ready {
			logger.Debug("页面加载完成")
			return nil
		}
		time.Sleep(500 * time.Millisecond)
	}

	logger.Warn("等待页面加载超时")
	return fmt.Errorf("页面加载超时")
}

// GetName 获取操作名称
func (a *WaitForPageLoadAction) GetName() string {
	return "WaitForPageLoad"
}

// GetPageInfoAction 获取页面信息操作
type GetPageInfoAction struct {
	StoreInContext bool
	ContextKey     string
}

// NewGetPageInfoAction 创建获取页面信息操作
func NewGetPageInfoAction() *GetPageInfoAction {
	return &GetPageInfoAction{
		StoreInContext: false,
		ContextKey:     "page_info",
	}
}

// WithContextStorage 设置将结果存储到上下文
func (a *GetPageInfoAction) WithContextStorage(key string) *GetPageInfoAction {
	a.StoreInContext = true
	a.ContextKey = key
	return a
}

// Execute 执行获取页面信息操作
func (a *GetPageInfoAction) Execute(ctx *ActionContext) error {
	script := `
		return {
			url: window.location.href,
			title: document.title,
			domain: window.location.hostname,
			pathname: window.location.pathname,
			search: window.location.search
		};
	`

	var result map[string]interface{}
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("获取页面信息失败: %w", err)
	}

	info := make(map[string]string)
	for key, value := range result {
		if str, ok := value.(string); ok {
			info[key] = str
		}
	}

	logger.Info("页面信息获取成功:")
	for key, value := range info {
		logger.Infof("  %s: %s", key, value)
	}

	return nil
}

// GetName 获取操作名称
func (a *GetPageInfoAction) GetName() string {
	return "GetPageInfo"
}
