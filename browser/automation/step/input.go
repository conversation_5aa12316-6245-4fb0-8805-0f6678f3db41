package step

import (
	"fmt"
	"time"

	"auto-register/browser/automation/action"
	"auto-register/logger"
)

// FillEmailStep 填写邮箱步骤
type FillEmailStep struct {
	Email          string
	TakeScreenshot bool
	ScreenshotName string
}

// NewFillEmailStep 创建填写邮箱步骤
func NewFillEmailStep(email string) *FillEmailStep {
	return &FillEmailStep{
		Email:          email,
		TakeScreenshot: true,
		ScreenshotName: "email_filled",
	}
}

// WithScreenshot 设置截图选项
func (s *FillEmailStep) WithScreenshot(take bool, name string) *FillEmailStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行填写邮箱步骤
func (s *FillEmailStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行填写邮箱步骤")

	// 使用预定义的邮箱输入选择器
	inputAction := action.NewInputWithSelectorsAction(action.CommonInputSelectors, s.Email).
		WithTimeout(10 * time.Second).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := inputAction.Execute(ctx); err != nil {
		return fmt.Errorf("填写邮箱失败: %w", err)
	}

	logger.Infof("邮箱填写完成: %s", s.Email)
	return nil
}

// GetName 获取步骤名称
func (s *FillEmailStep) GetName() string {
	return "FillEmail"
}

// FillUsernameStep 填写用户名步骤
type FillUsernameStep struct {
	Username       string
	TakeScreenshot bool
	ScreenshotName string
}

// NewFillUsernameStep 创建填写用户名步骤
func NewFillUsernameStep(username string) *FillUsernameStep {
	return &FillUsernameStep{
		Username:       username,
		TakeScreenshot: true,
		ScreenshotName: "username_filled",
	}
}

// WithScreenshot 设置截图选项
func (s *FillUsernameStep) WithScreenshot(take bool, name string) *FillUsernameStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行填写用户名步骤
func (s *FillUsernameStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行填写用户名步骤")

	// 用户名选择器
	usernameSelectors := action.ElementSelectors{
		`input[name="username"]`,
		`input[name="user"]`,
		`input[name="login"]`,
		`input[id="username"]`,
		`input[id="user"]`,
		`input[id="login"]`,
		`input[data-testid="username"]`,
		`input[placeholder*="username" i]`,
		`input[placeholder*="用户名" i]`,
		`input[type="text"]`,
	}

	inputAction := action.NewInputWithSelectorsAction(usernameSelectors, s.Username).
		WithTimeout(10 * time.Second).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := inputAction.Execute(ctx); err != nil {
		return fmt.Errorf("填写用户名失败: %w", err)
	}

	logger.Infof("用户名填写完成: %s", s.Username)
	return nil
}

// GetName 获取步骤名称
func (s *FillUsernameStep) GetName() string {
	return "FillUsername"
}

// FillPasswordStep 填写密码步骤
type FillPasswordStep struct {
	Password       string
	TakeScreenshot bool
	ScreenshotName string
}

// NewFillPasswordStep 创建填写密码步骤
func NewFillPasswordStep(password string) *FillPasswordStep {
	return &FillPasswordStep{
		Password:       password,
		TakeScreenshot: true,
		ScreenshotName: "password_filled",
	}
}

// WithScreenshot 设置截图选项
func (s *FillPasswordStep) WithScreenshot(take bool, name string) *FillPasswordStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行填写密码步骤
func (s *FillPasswordStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行填写密码步骤")

	inputAction := action.NewInputWithSelectorsAction(action.PasswordInputSelectors, s.Password).
		WithTimeout(10 * time.Second).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := inputAction.Execute(ctx); err != nil {
		return fmt.Errorf("填写密码失败: %w", err)
	}

	logger.Info("密码填写完成")
	return nil
}

// GetName 获取步骤名称
func (s *FillPasswordStep) GetName() string {
	return "FillPassword"
}

// FillFirstNameStep 填写名字步骤
type FillFirstNameStep struct {
	FirstName      string
	TakeScreenshot bool
	ScreenshotName string
}

// NewFillFirstNameStep 创建填写名字步骤
func NewFillFirstNameStep(firstName string) *FillFirstNameStep {
	return &FillFirstNameStep{
		FirstName:      firstName,
		TakeScreenshot: true,
		ScreenshotName: "firstname_filled",
	}
}

// WithScreenshot 设置截图选项
func (s *FillFirstNameStep) WithScreenshot(take bool, name string) *FillFirstNameStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行填写名字步骤
func (s *FillFirstNameStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行填写名字步骤")

	firstNameSelectors := action.ElementSelectors{
		`input[name="firstName"]`,
		`input[name="first_name"]`,
		`input[name="fname"]`,
		`input[id="firstName"]`,
		`input[id="first_name"]`,
		`input[id="fname"]`,
		`input[data-testid="firstName"]`,
		`input[placeholder*="first name" i]`,
		`input[placeholder*="名字" i]`,
		`input[placeholder*="姓名" i]`,
	}

	inputAction := action.NewInputWithSelectorsAction(firstNameSelectors, s.FirstName).
		WithTimeout(10 * time.Second).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := inputAction.Execute(ctx); err != nil {
		return fmt.Errorf("填写名字失败: %w", err)
	}

	logger.Infof("名字填写完成: %s", s.FirstName)
	return nil
}

// GetName 获取步骤名称
func (s *FillFirstNameStep) GetName() string {
	return "FillFirstName"
}

// FillLastNameStep 填写姓氏步骤
type FillLastNameStep struct {
	LastName       string
	TakeScreenshot bool
	ScreenshotName string
}

// NewFillLastNameStep 创建填写姓氏步骤
func NewFillLastNameStep(lastName string) *FillLastNameStep {
	return &FillLastNameStep{
		LastName:       lastName,
		TakeScreenshot: true,
		ScreenshotName: "lastname_filled",
	}
}

// WithScreenshot 设置截图选项
func (s *FillLastNameStep) WithScreenshot(take bool, name string) *FillLastNameStep {
	s.TakeScreenshot = take
	s.ScreenshotName = name
	return s
}

// Execute 执行填写姓氏步骤
func (s *FillLastNameStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行填写姓氏步骤")

	lastNameSelectors := action.ElementSelectors{
		`input[name="lastName"]`,
		`input[name="last_name"]`,
		`input[name="lname"]`,
		`input[id="lastName"]`,
		`input[id="last_name"]`,
		`input[id="lname"]`,
		`input[data-testid="lastName"]`,
		`input[placeholder*="last name" i]`,
		`input[placeholder*="姓氏" i]`,
		`input[placeholder*="姓" i]`,
	}

	inputAction := action.NewInputWithSelectorsAction(lastNameSelectors, s.LastName).
		WithTimeout(10 * time.Second).
		WithScreenshot(s.TakeScreenshot, s.ScreenshotName)

	if err := inputAction.Execute(ctx); err != nil {
		return fmt.Errorf("填写姓氏失败: %w", err)
	}

	logger.Infof("姓氏填写完成: %s", s.LastName)
	return nil
}

// GetName 获取步骤名称
func (s *FillLastNameStep) GetName() string {
	return "FillLastName"
}
