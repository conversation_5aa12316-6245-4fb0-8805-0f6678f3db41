package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"auto-register/browser"
	"auto-register/browser/automation/action"
	"auto-register/browser/automation/flow"
	"auto-register/browser/automation/step"
	"auto-register/config"
	"auto-register/generator"
	"auto-register/logger"
)

func main() {
	// 初始化日志
	if err := logger.InitLogger("INFO", ""); err != nil {
		log.Fatalf("日志初始化失败: %v", err)
	}

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 从环境变量获取登录凭据
	email := os.Getenv("AUGMENT_EMAIL")
	password := os.Getenv("AUGMENT_PASSWORD")

	if email == "" || password == "" {
		fmt.Println("使用方法:")
		fmt.Println("export AUGMENT_EMAIL=<EMAIL>")
		fmt.Println("export AUGMENT_PASSWORD=your-password")
		fmt.Println("go run examples/three-layer-architecture-example.go")
		os.Exit(1)
	}

	// 初始化浏览器管理器
	browserManager := browser.NewManager(cfg)
	if err := browserManager.Start(); err != nil {
		log.Fatalf("启动浏览器失败: %v", err)
	}
	defer browserManager.Stop()

	// 创建操作上下文
	ctx := action.NewActionContext(browserManager)

	// 演示三层架构的使用
	demonstrateThreeLayerArchitecture(ctx, email, password)

	fmt.Println("\n按回车键退出...")
	fmt.Scanln()
}

func demonstrateThreeLayerArchitecture(ctx *action.ActionContext, email, password string) {
	fmt.Println("\n=== 三层架构演示 ===")

	// 第一层：Action层 - 基础元素操作
	fmt.Println("\n1. Action层演示 - 基础元素操作:")
	demonstrateActionLayer(ctx, email, password)

	// 第二层：Step层 - 常用处理步骤
	fmt.Println("\n2. Step层演示 - 常用处理步骤:")
	demonstrateStepLayer(ctx, email, password)

	// 第三层：Flow层 - 业务流程
	fmt.Println("\n3. Flow层演示 - 业务流程:")
	demonstrateFlowLayer(ctx, email, password)
}

// 演示Action层 - 基础元素操作
func demonstrateActionLayer(ctx *action.ActionContext, email, password string) {
	logger.Info("演示Action层 - 基础元素操作")

	// 1. 导航操作
	navigateAction := action.NewNavigateAction("https://login.augmentcode.com/").
		WithScreenshot(true, "action_navigate")
	if err := navigateAction.Execute(ctx); err != nil {
		logger.Errorf("导航操作失败: %v", err)
		return
	}

	// 2. 输入操作
	emailInputAction := action.NewInputWithSelectorsAction(action.CommonInputSelectors, email).
		WithScreenshot(true, "action_email_input")
	if err := emailInputAction.Execute(ctx); err != nil {
		logger.Errorf("邮箱输入操作失败: %v", err)
		return
	}

	// 3. 按钮操作
	continueButtonAction := action.NewButtonClickAction(action.ContinueButtonSelectors).
		WithScreenshot(true, "action_continue_click")
	if err := continueButtonAction.Execute(ctx); err != nil {
		logger.Errorf("继续按钮操作失败: %v", err)
		return
	}

	// 4. Turnstile操作
	turnstileAction := action.NewTurnstileAction().WithTimeout(10 * time.Second)
	if err := turnstileAction.Execute(ctx); err != nil {
		logger.Warnf("Turnstile操作失败（可能不存在）: %v", err)
	}

	// 5. 调试操作
	screenshotAction := action.NewScreenshotAction("action_layer_demo")
	if err := screenshotAction.Execute(ctx); err != nil {
		logger.Warnf("截图操作失败: %v", err)
	}

	logger.Info("Action层演示完成")
}

// 演示Step层 - 常用处理步骤
func demonstrateStepLayer(ctx *action.ActionContext, email, password string) {
	logger.Info("演示Step层 - 常用处理步骤")

	// 1. 导航步骤
	navigateStep := step.NewNavigateToLoginPageStep("https://login.augmentcode.com/").
		WithScreenshot(true, "step_navigate")
	if err := navigateStep.Execute(ctx); err != nil {
		logger.Errorf("导航步骤失败: %v", err)
		return
	}

	// 2. 填写邮箱步骤
	fillEmailStep := step.NewFillEmailStep(email).
		WithScreenshot(true, "step_email_filled")
	if err := fillEmailStep.Execute(ctx); err != nil {
		logger.Errorf("填写邮箱步骤失败: %v", err)
		return
	}

	// 3. 点击继续步骤
	clickContinueStep := step.NewClickContinueStep().
		WithScreenshot(true, "step_continue_clicked")
	if err := clickContinueStep.Execute(ctx); err != nil {
		logger.Errorf("点击继续步骤失败: %v", err)
		return
	}

	// 4. 填写密码步骤
	fillPasswordStep := step.NewFillPasswordStep(password).
		WithScreenshot(true, "step_password_filled")
	if err := fillPasswordStep.Execute(ctx); err != nil {
		logger.Errorf("填写密码步骤失败: %v", err)
		return
	}

	// 5. 点击登录步骤
	clickLoginStep := step.NewClickLoginStep().
		WithScreenshot(true, "step_login_clicked")
	if err := clickLoginStep.Execute(ctx); err != nil {
		logger.Errorf("点击登录步骤失败: %v", err)
		return
	}

	// 6. 处理Turnstile步骤
	handleTurnstileStep := step.NewHandleTurnstileStep().WithRequired(false)
	if err := handleTurnstileStep.Execute(ctx); err != nil {
		logger.Warnf("处理Turnstile步骤失败: %v", err)
	}

	// 7. 验证登录结果步骤
	verifyStep := step.NewVerifyLoginResultStep().
		WithAllowUnknownStatus(true)
	if err := verifyStep.Execute(ctx); err != nil {
		logger.Errorf("验证登录结果步骤失败: %v", err)
		return
	}

	logger.Info("Step层演示完成")
}

// 演示Flow层 - 业务流程
func demonstrateFlowLayer(ctx *action.ActionContext, email, password string) {
	logger.Info("演示Flow层 - 业务流程")

	// 1. Augment登录流程
	augmentFlow := flow.NewAugmentLoginFlow()

	// 快速登录
	if err := augmentFlow.QuickLogin(ctx, email, password); err != nil {
		logger.Errorf("Augment快速登录失败: %v", err)
		return
	}

	// 完整登录流程
	if err := augmentFlow.LoginWithCredentials(ctx, email, password); err != nil {
		logger.Errorf("Augment完整登录失败: %v", err)
		return
	}

	// 调试登录流程
	if err := augmentFlow.DebugLogin(ctx, email, password); err != nil {
		logger.Errorf("Augment调试登录失败: %v", err)
		return
	}

	// 2. 使用账号信息
	account := &generator.AccountInfo{
		Email:     email,
		Password:  password,
		FirstName: "Test",
		LastName:  "User",
	}

	if err := augmentFlow.LoginWithAccount(ctx, account); err != nil {
		logger.Errorf("使用账号信息登录失败: %v", err)
		return
	}

	// 3. Cursor注册流程演示（如果需要）
	cursorFlow := flow.NewCursorRegisterFlow()
	logger.Infof("Cursor流程已创建: %s", cursorFlow.GetName())

	logger.Info("Flow层演示完成")
}

// 演示混合使用
func demonstrateMixedUsage(ctx *action.ActionContext, email, password string) {
	logger.Info("演示混合使用三层架构")

	// 可以在Flow中混合使用Action和Step
	sequence := step.NewStepSequence("MixedUsage").
		// 使用Step层
		Add(step.NewNavigateToLoginPageStep("https://login.augmentcode.com/")).
		Add(step.NewFillEmailStep(email)).
		// 混合使用Action层
		Add(&ActionWrapper{Action: action.NewDelayAction(2 * time.Second)}).
		// 继续使用Step层
		Add(step.NewClickContinueStep()).
		Add(step.NewFillPasswordStep(password)).
		Add(step.NewClickLoginStep()).
		Add(step.NewVerifyLoginResultStep())

	if err := sequence.Execute(ctx); err != nil {
		logger.Errorf("混合使用失败: %v", err)
		return
	}

	logger.Info("混合使用演示完成")
}

// ActionWrapper 将Action包装为Step
type ActionWrapper struct {
	Action action.Action
}

// Execute 执行包装的Action
func (w *ActionWrapper) Execute(ctx *action.ActionContext) error {
	return w.Action.Execute(ctx)
}

// GetName 获取名称
func (w *ActionWrapper) GetName() string {
	return fmt.Sprintf("ActionWrapper[%s]", w.Action.GetName())
}
