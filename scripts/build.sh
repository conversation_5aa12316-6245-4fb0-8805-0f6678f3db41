#!/bin/bash

# Auto Register Framework 构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="auto-register"
VERSION="v1.0.0"
BUILD_DIR="bin"
DIST_DIR="dist"

echo -e "${BLUE}=== Auto Register Framework 构建脚本 ===${NC}"
echo -e "${BLUE}版本: ${VERSION}${NC}"
echo ""

# 清理旧的构建文件
echo -e "${YELLOW}清理旧的构建文件...${NC}"
rm -rf ${BUILD_DIR}
rm -rf ${DIST_DIR}
mkdir -p ${BUILD_DIR}
mkdir -p ${DIST_DIR}

# 检查Go环境
echo -e "${YELLOW}检查Go环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}错误: 未找到Go环境${NC}"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}')
echo -e "${GREEN}Go版本: ${GO_VERSION}${NC}"

# 安装依赖
echo -e "${YELLOW}安装依赖...${NC}"
go mod tidy
go mod download

# 运行测试
echo -e "${YELLOW}运行测试...${NC}"
go test ./... -v

# 构建不同平台的二进制文件
echo -e "${YELLOW}构建二进制文件...${NC}"

# 定义构建目标
declare -a targets=(
    "linux/amd64"
    "linux/arm64"
    "windows/amd64"
    "darwin/amd64"
    "darwin/arm64"
)

# 构建每个目标
for target in "${targets[@]}"; do
    IFS='/' read -r GOOS GOARCH <<< "$target"
    
    echo -e "${BLUE}构建 ${GOOS}/${GOARCH}...${NC}"
    
    # 设置输出文件名
    output_name="${PROJECT_NAME}-${GOOS}-${GOARCH}"
    if [ "$GOOS" = "windows" ]; then
        output_name="${output_name}.exe"
    fi
    
    # 构建
    env GOOS=$GOOS GOARCH=$GOARCH go build \
        -ldflags "-X main.AppVersion=${VERSION} -s -w" \
        -o "${BUILD_DIR}/${output_name}" \
        main.go
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ ${output_name} 构建成功${NC}"
        
        # 创建发布包
        package_name="${PROJECT_NAME}-${VERSION}-${GOOS}-${GOARCH}"
        package_dir="${DIST_DIR}/${package_name}"
        
        mkdir -p "${package_dir}"
        
        # 复制文件到发布包
        cp "${BUILD_DIR}/${output_name}" "${package_dir}/"
        cp .env.example "${package_dir}/"
        cp README.md "${package_dir}/"
        cp USAGE.md "${package_dir}/"
        cp LICENSE "${package_dir}/"
        cp names-dataset.txt "${package_dir}/"
        
        # 创建压缩包
        cd "${DIST_DIR}"
        if [ "$GOOS" = "windows" ]; then
            zip -r "${package_name}.zip" "${package_name}/"
        else
            tar -czf "${package_name}.tar.gz" "${package_name}/"
        fi
        cd ..
        
        # 清理临时目录
        rm -rf "${package_dir}"
        
        echo -e "${GREEN}✓ ${package_name} 打包完成${NC}"
    else
        echo -e "${RED}✗ ${output_name} 构建失败${NC}"
    fi
done

# 显示构建结果
echo ""
echo -e "${GREEN}=== 构建完成 ===${NC}"
echo -e "${BLUE}二进制文件:${NC}"
ls -la ${BUILD_DIR}/

echo ""
echo -e "${BLUE}发布包:${NC}"
ls -la ${DIST_DIR}/

# 计算文件大小
echo ""
echo -e "${BLUE}文件大小统计:${NC}"
for file in ${BUILD_DIR}/*; do
    if [ -f "$file" ]; then
        size=$(du -h "$file" | cut -f1)
        filename=$(basename "$file")
        echo -e "${GREEN}${filename}: ${size}${NC}"
    fi
done

echo ""
echo -e "${GREEN}构建脚本执行完成！${NC}"
echo -e "${YELLOW}提示: 可以使用 'make release' 命令执行此脚本${NC}"
