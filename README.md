# Auto Register Framework

一个基于Go语言实现的自动注册框架，参考了 [cursor-auto-free](https://github.com/chengazhen/cursor-auto-free) 项目的设计思路。

## 功能特性

- 🚀 **多流程支持**: 支持Cursor注册、Augment登录等多种自动化流程
- 🏗️ **三层架构**: Action/Step/Flow三层架构，模块化设计
- 🎯 **模式选择**: 支持标准、快速、调试三种执行模式
- 🌐 **浏览器自动化**: 使用chromedp控制浏览器完成自动化流程
- 📧 **邮箱验证支持**: 支持临时邮箱和IMAP邮箱接收验证码
- 🔐 **验证码处理**: 自动处理Turnstile验证码
- 💾 **认证管理**: 自动更新Cursor的本地认证信息
- 📝 **完整日志**: 详细的操作日志记录
- 🖼️ **截图调试**: 自动保存关键步骤截图
- ⚙️ **灵活配置**: 支持多种配置选项和环境变量

## 项目结构

```
auto-register/
├── auth/                   # 认证管理
│   └── cursor.go          # Cursor认证信息管理
├── automation/            # 自动化管理器
│   ├── interfaces.go      # 接口定义
│   └── manager.go         # 自动化流程管理器
├── browser/               # 浏览器自动化
│   ├── automation/        # 三层架构自动化
│   │   ├── action/        # Action层 - 基础元素操作
│   │   │   ├── base.go    # 基础接口和工具
│   │   │   ├── input.go   # 输入框操作
│   │   │   ├── button.go  # 按钮操作
│   │   │   ├── navigate.go # 页面导航
│   │   │   ├── turnstile.go # Turnstile验证
│   │   │   ├── verify.go  # 结果验证
│   │   │   ├── debug.go   # 调试工具
│   │   │   └── sequence.go # 操作序列
│   │   ├── step/          # Step层 - 常用处理步骤
│   │   │   ├── base.go    # 步骤基础接口
│   │   │   ├── input.go   # 表单填写步骤
│   │   │   ├── button.go  # 按钮点击步骤
│   │   │   ├── navigation.go # 导航步骤
│   │   │   └── verification.go # 验证步骤
│   │   └── flow/          # Flow层 - 业务流程
│   │       ├── augment.go # Augment登录流程
│   │       └── cursor.go  # Cursor注册流程
│   ├── manager.go         # 浏览器管理器
│   ├── registration_compat.go # 兼容层
│   └── utils.go           # 工具函数
├── config/                # 配置管理
│   └── config.go          # 配置加载和验证
├── email/                 # 邮箱处理
│   ├── handler.go         # 邮箱处理器接口
│   ├── imap.go            # IMAP邮箱支持
│   └── temp.go            # 临时邮箱支持
├── generator/             # 账号生成
│   ├── account.go         # 账号信息生成
│   └── names.go           # 姓名生成器
├── logger/                # 日志系统
│   └── logger.go          # 日志管理
├── examples/              # 示例代码
│   └── three-layer-architecture-example.go # 三层架构示例
├── main.go                # 主程序入口
├── go.mod                 # Go模块定义
├── .env.example           # 环境变量示例
├── THREE_LAYER_ARCHITECTURE.md # 三层架构文档
├── names-dataset.txt      # 姓名数据集
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境要求

- Go 1.21 或更高版本
- Chrome/Chromium 浏览器

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并根据需要修改配置：

```bash
cp .env.example .env
```

### 4. 配置说明

#### 自动化流程配置
```env
# 选择自动化流程: cursor_register, augment_login, custom
AUTOMATION_FLOW=cursor_register
```

#### 基础配置
```env
# 域名配置 - 你的 Cloudflare 域名
DOMAIN=wozhangsan.me
```

#### 邮箱配置（二选一）

**选项1: 临时邮箱模式**
```env
TEMP_MAIL=xxxxxx
TEMP_MAIL_EPIN=xxxxxx
TEMP_MAIL_EXT=@mailto.plus
```

**选项2: IMAP邮箱模式**
```env
TEMP_MAIL=null  # 设置为null启用IMAP模式
IMAP_SERVER=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password
IMAP_DIR=inbox
IMAP_PROTOCOL=IMAP
```

#### 浏览器配置
```env
BROWSER_HEADLESS=false  # 建议调试时设为false
BROWSER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
```

#### 流程特定配置
```env
# Cursor注册流程配置
FLOW_CURSOR_BASE_URL=https://cursor.sh/sign-up
FLOW_CURSOR_DEBUG_MODE=false
FLOW_CURSOR_QUICK_MODE=false

# Augment登录流程配置
FLOW_AUGMENT_BASE_URL=https://login.augmentcode.com/
FLOW_AUGMENT_DEBUG_MODE=false
FLOW_AUGMENT_QUICK_MODE=false

# 通用流程配置
FLOW_ENABLE_SCREENSHOTS=true
FLOW_SCREENSHOT_DIR=screenshots
FLOW_MAX_RETRIES=3
FLOW_RETRY_DELAY=5
```

### 5. 运行程序

```bash
go run main.go
```

## 使用说明

### 操作模式

程序启动后会提示选择操作模式：

1. **仅重置机器码**: 只重置设备标识
2. **完整自动化流程**: 执行配置的自动化流程

### 支持的自动化流程

#### 1. Cursor注册流程 (`cursor_register`)
- **标准模式**: 完整的注册流程，包含所有步骤
- **快速模式**: 最小化步骤的快速注册
- **调试模式**: 包含详细调试信息的注册流程

#### 2. Augment登录流程 (`augment_login`)
- **标准模式**: 完整的登录流程
- **快速模式**: 最小化步骤的快速登录
- **调试模式**: 包含详细调试信息的登录流程

#### 3. 自定义流程 (`custom`)
- 可在 `automation/manager.go` 中自定义实现
- 支持组合多个步骤和操作序列

### 自动化流程执行

#### 三层架构说明

**Action层** - 基础元素操作
- 输入框操作、按钮点击、页面导航等原子性操作
- 每个操作都是最小的、可重用的单元

**Step层** - 常用处理步骤
- 将Action层操作组合成有意义的业务步骤
- 如填写邮箱、点击登录、验证结果等

**Flow层** - 业务流程实现
- 组合Step层步骤完成完整的业务操作
- 如完整的注册流程、登录流程等

#### 执行流程示例（Cursor注册）

1. 生成随机账号信息（邮箱、密码、姓名）
2. 启动浏览器并导航到注册页面
3. 自动填写个人信息
4. 处理Turnstile验证码
5. 设置密码
6. 处理邮箱验证（如需要）
7. 获取会话令牌
8. 更新Cursor本地认证信息

### 邮箱验证

框架支持两种邮箱验证方式：

#### 临时邮箱
- 使用 tempmail.plus 服务
- 需要配置 `TEMP_MAIL` 和 `TEMP_MAIL_EPIN`
- 自动接收和解析验证码

#### IMAP邮箱
- 支持Gmail、QQ邮箱、163邮箱等
- 需要配置IMAP服务器信息
- 支持网易系邮箱的特殊处理

## 配置详解

### 环境变量说明

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `DOMAIN` | 用于生成邮箱的域名 | - | ✅ |
| `TEMP_MAIL` | 临时邮箱用户名（设为null启用IMAP） | - | ✅ |
| `TEMP_MAIL_EPIN` | 临时邮箱PIN码 | - | 临时邮箱模式必填 |
| `TEMP_MAIL_EXT` | 临时邮箱后缀 | @mailto.plus | ❌ |
| `IMAP_SERVER` | IMAP服务器地址 | - | IMAP模式必填 |
| `IMAP_PORT` | IMAP端口 | 993 | ❌ |
| `IMAP_USER` | IMAP用户名 | - | IMAP模式必填 |
| `IMAP_PASS` | IMAP密码/授权码 | - | IMAP模式必填 |
| `IMAP_DIR` | IMAP邮箱目录 | inbox | ❌ |
| `IMAP_PROTOCOL` | 邮箱协议 | IMAP | ❌ |
| `BROWSER_HEADLESS` | 无头模式 | true | ❌ |
| `BROWSER_PATH` | 浏览器路径 | - | ❌ |
| `BROWSER_PROXY` | 代理设置 | - | ❌ |
| `LOG_LEVEL` | 日志级别 | info | ❌ |
| `MAX_RETRIES` | 最大重试次数 | 3 | ❌ |
| `RETRY_INTERVAL` | 重试间隔(秒) | 60 | ❌ |

### 支持的IMAP邮箱

- **Gmail**: imap.gmail.com:993
- **QQ邮箱**: imap.qq.com:993
- **163邮箱**: imap.163.com:993
- **126邮箱**: imap.126.com:993
- **Outlook**: outlook.office365.com:993

## 开发说明

### 添加新的邮箱支持

1. 在 `email/` 目录下创建新的处理器
2. 实现 `Handler` 接口
3. 在 `email/handler.go` 中注册新的处理器

### 自定义浏览器操作

在 `browser/automation.go` 中修改或添加新的自动化逻辑。

### 扩展认证管理

在 `auth/cursor.go` 中添加新的认证相关功能。

## 注意事项

1. **合法使用**: 请确保在合法合规的前提下使用本工具
2. **频率控制**: 避免过于频繁的注册操作
3. **邮箱配置**: 使用IMAP时需要开启邮箱的IMAP服务并获取授权码
4. **浏览器版本**: 确保Chrome/Chromium版本与chromedp兼容
5. **网络环境**: 确保网络连接稳定，必要时配置代理

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome是否正确安装
   - 尝试指定浏览器路径

2. **邮箱验证失败**
   - 检查IMAP配置是否正确
   - 确认邮箱授权码是否有效

3. **Turnstile验证码失败**
   - 查看 `screenshots/` 目录下的截图
   - 检查 `debug/` 目录下的HTML文件
   - 参考 `TURNSTILE_DEBUG.md` 详细调试指南
   - 尝试启用有界面模式：`BROWSER_HEADLESS=false`
   - 考虑使用代理：`BROWSER_PROXY=http://proxy:port`

### 日志分析

程序会生成详细的日志文件，可以通过日志分析问题：

```bash
tail -f auto-register.log
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 致谢

- 感谢 [cursor-auto-free](https://github.com/chengazhen/cursor-auto-free) 项目提供的设计思路
- 感谢所有开源项目的贡献者

## 免责声明

本项目仅供学习交流使用，请勿用于商业用途。使用本项目造成的任何后果，由使用者自行承担。
