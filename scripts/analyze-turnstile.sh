#!/bin/bash

# Turnstile验证码分析脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Turnstile验证码分析脚本 ===${NC}"
echo ""

# 检查截图文件
echo -e "${YELLOW}检查截图文件...${NC}"
if [ -d "screenshots" ]; then
    echo -e "${GREEN}找到截图目录${NC}"
    
    # 分析截图文件
    for file in screenshots/turnstile_*.png; do
        if [ -f "$file" ]; then
            size=$(du -h "$file" | cut -f1)
            echo "  - $(basename "$file"): $size"
        fi
    done
    
    # 检查最新的失败截图
    if [ -f "screenshots/turnstile_failed.png" ]; then
        echo ""
        echo -e "${YELLOW}最新失败截图: screenshots/turnstile_failed.png${NC}"
        echo "建议：在浏览器中打开此截图查看Turnstile状态"
    fi
else
    echo -e "${RED}未找到截图目录${NC}"
fi

echo ""

# 检查调试HTML文件
echo -e "${YELLOW}检查调试HTML文件...${NC}"
if [ -d "debug" ]; then
    echo -e "${GREEN}找到调试目录${NC}"
    
    for file in debug/*.html; do
        if [ -f "$file" ]; then
            size=$(du -h "$file" | cut -f1)
            echo "  - $(basename "$file"): $size"
            
            # 分析HTML内容
            if [ -f "$file" ]; then
                echo "    分析 $(basename "$file"):"
                
                # 检查Turnstile元素
                if grep -q "cf-turnstile" "$file"; then
                    echo "    ✓ 包含Turnstile元素"
                else
                    echo "    ✗ 未找到Turnstile元素"
                fi
                
                # 检查iframe
                if grep -q "iframe" "$file"; then
                    echo "    ✓ 包含iframe元素"
                else
                    echo "    ✗ 未找到iframe元素"
                fi
                
                # 检查错误信息
                if grep -qi "error\|failed\|blocked" "$file"; then
                    echo "    ⚠ 可能包含错误信息"
                fi
                
                # 检查验证码相关的class
                if grep -qi "success\|verified\|completed" "$file"; then
                    echo "    ✓ 可能包含成功标识"
                fi
            fi
        fi
    done
else
    echo -e "${RED}未找到调试目录${NC}"
fi

echo ""

# 分析日志文件
echo -e "${YELLOW}分析日志文件...${NC}"
if [ -f "auto-register.log" ]; then
    echo -e "${GREEN}找到日志文件${NC}"
    
    # 获取最近的Turnstile相关日志
    echo ""
    echo -e "${BLUE}最近的Turnstile日志:${NC}"
    tail -50 auto-register.log | grep -i turnstile | tail -10
    
    echo ""
    echo -e "${BLUE}验证方法尝试统计:${NC}"
    
    # 统计各种方法的尝试次数
    method1_count=$(grep -c "方法1" auto-register.log 2>/dev/null || echo "0")
    method2_count=$(grep -c "方法2" auto-register.log 2>/dev/null || echo "0")
    method3_count=$(grep -c "方法3" auto-register.log 2>/dev/null || echo "0")
    method4_count=$(grep -c "方法4" auto-register.log 2>/dev/null || echo "0")
    
    echo "  - 方法1 (iframe复选框): $method1_count 次"
    echo "  - 方法2 (iframe点击): $method2_count 次"
    echo "  - 方法3 (用户交互): $method3_count 次"
    echo "  - 方法4 (chromedp点击): $method4_count 次"
    
    echo ""
    echo -e "${BLUE}错误统计:${NC}"
    
    # 统计错误类型
    timeout_count=$(grep -c "超时" auto-register.log 2>/dev/null || echo "0")
    failed_count=$(grep -c "验证失败" auto-register.log 2>/dev/null || echo "0")
    
    echo "  - 超时错误: $timeout_count 次"
    echo "  - 验证失败: $failed_count 次"
    
else
    echo -e "${RED}未找到日志文件${NC}"
fi

echo ""

# 提供建议
echo -e "${BLUE}=== 分析建议 ===${NC}"

# 检查网络相关问题
echo -e "${YELLOW}网络检查:${NC}"
if ping -c 1 challenges.cloudflare.com >/dev/null 2>&1; then
    echo "  ✓ 可以访问Cloudflare服务器"
else
    echo "  ✗ 无法访问Cloudflare服务器，可能需要代理"
fi

echo ""
echo -e "${YELLOW}配置建议:${NC}"

# 检查当前配置
if [ -f ".env" ]; then
    if grep -q "BROWSER_HEADLESS=false" .env; then
        echo "  ✓ 已启用有界面模式，便于观察"
    else
        echo "  建议启用有界面模式: BROWSER_HEADLESS=false"
    fi
    
    if grep -q "LOG_LEVEL=debug" .env; then
        echo "  ✓ 已启用调试日志"
    else
        echo "  建议启用调试日志: LOG_LEVEL=debug"
    fi
    
    if grep -q "BROWSER_PROXY" .env && ! grep -q "BROWSER_PROXY=$" .env; then
        echo "  ✓ 已配置代理"
    else
        echo "  如果网络有问题，建议配置代理: BROWSER_PROXY=http://proxy:port"
    fi
else
    echo "  ✗ 未找到配置文件"
fi

echo ""
echo -e "${YELLOW}下一步建议:${NC}"

# 根据分析结果提供建议
if [ -f "screenshots/turnstile_failed.png" ]; then
    echo "1. 查看失败截图: screenshots/turnstile_failed.png"
fi

if [ -f "debug/turnstile_failed.html" ]; then
    echo "2. 分析HTML文件: debug/turnstile_failed.html"
    echo "   在浏览器中打开并检查Turnstile元素"
fi

echo "3. 尝试不同的配置:"
echo "   - 启用有界面模式观察过程"
echo "   - 增加重试次数和间隔"
echo "   - 使用代理服务器"

echo "4. 手动测试:"
echo "   - 在浏览器中访问 https://authenticator.cursor.sh/sign-up"
echo "   - 观察Turnstile验证码的行为"

echo ""
echo -e "${GREEN}分析完成！${NC}"
echo "如需更多帮助，请查看 TURNSTILE_DEBUG.md"
