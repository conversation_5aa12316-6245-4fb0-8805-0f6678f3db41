#!/bin/bash

# Turnstile验证码测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Turnstile验证码测试脚本 ===${NC}"
echo ""

# 检查程序是否存在
if [ ! -f "./auto-register" ]; then
    echo -e "${RED}错误: 未找到 auto-register 程序${NC}"
    echo "请先运行: go build -o auto-register main.go"
    exit 1
fi

# 检查配置文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}警告: 未找到 .env 配置文件${NC}"
    echo "使用默认配置进行测试..."
    cp .env.example .env
fi

# 创建测试配置
echo -e "${YELLOW}创建测试配置...${NC}"
cat > .env.test << EOF
# 测试配置
DOMAIN=test-domain.com
TEMP_MAIL=testuser
TEMP_MAIL_EPIN=123456
TEMP_MAIL_EXT=@mailto.plus

# 调试模式
BROWSER_HEADLESS=false
LOG_LEVEL=debug

# 增加重试次数用于测试
MAX_RETRIES=3
RETRY_INTERVAL=5
VERIFICATION_TIMEOUT=60
EOF

# 备份原配置
if [ -f ".env" ]; then
    cp .env .env.backup
    echo -e "${GREEN}原配置已备份为 .env.backup${NC}"
fi

# 使用测试配置
cp .env.test .env

echo -e "${BLUE}测试配置:${NC}"
echo "- 域名: test-domain.com"
echo "- 浏览器: 有界面模式 (可观察验证过程)"
echo "- 日志级别: debug"
echo "- 重试次数: 3"
echo ""

echo -e "${YELLOW}注意事项:${NC}"
echo "1. 此测试将打开浏览器窗口"
echo "2. 您可以观察Turnstile验证码的处理过程"
echo "3. 测试完成后会自动恢复原配置"
echo "4. 截图将保存在 screenshots/ 目录"
echo "5. 调试HTML将保存在 debug/ 目录"
echo ""

read -p "按回车键开始测试..."

echo -e "${BLUE}开始Turnstile验证码测试...${NC}"

# 运行测试
echo "2" | timeout 120s ./auto-register || {
    exit_code=$?
    if [ $exit_code -eq 124 ]; then
        echo -e "${YELLOW}测试超时 (120秒)${NC}"
    else
        echo -e "${RED}测试失败，退出码: $exit_code${NC}"
    fi
}

echo ""
echo -e "${BLUE}=== 测试结果分析 ===${NC}"

# 检查截图
if [ -d "screenshots" ]; then
    echo -e "${GREEN}截图文件:${NC}"
    ls -la screenshots/turnstile_* 2>/dev/null || echo "无Turnstile相关截图"
    echo ""
fi

# 检查调试文件
if [ -d "debug" ]; then
    echo -e "${GREEN}调试文件:${NC}"
    ls -la debug/ 2>/dev/null || echo "无调试文件"
    echo ""
fi

# 检查日志
if [ -f "auto-register.log" ]; then
    echo -e "${GREEN}最近的Turnstile相关日志:${NC}"
    tail -20 auto-register.log | grep -i turnstile || echo "无Turnstile相关日志"
    echo ""
fi

# 恢复原配置
if [ -f ".env.backup" ]; then
    mv .env.backup .env
    echo -e "${GREEN}原配置已恢复${NC}"
else
    rm -f .env
    echo -e "${YELLOW}已删除测试配置${NC}"
fi

# 清理测试配置
rm -f .env.test

echo ""
echo -e "${BLUE}=== 调试建议 ===${NC}"
echo "1. 查看截图了解验证码状态:"
echo "   - turnstile_start.png: 验证开始"
echo "   - turnstile_clicked.png: 点击后状态"
echo "   - turnstile_failed.png: 失败状态"
echo ""
echo "2. 查看HTML调试文件:"
echo "   - debug/turnstile_failed.html"
echo ""
echo "3. 查看详细日志:"
echo "   - tail -f auto-register.log | grep -i turnstile"
echo ""
echo "4. 参考调试指南:"
echo "   - cat TURNSTILE_DEBUG.md"
echo ""

echo -e "${GREEN}测试完成！${NC}"
