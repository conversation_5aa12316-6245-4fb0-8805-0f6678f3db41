package action

import (
	"fmt"
	"time"

	"auto-register/logger"
)

// InputElementAction 基础输入元素操作
type InputElementAction struct {
	Selector       string
	Value          string
	ClearFirst     bool
	ClickFirst     bool
	WaitTimeout    time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewInputElementAction 创建基础输入元素操作
func NewInputElementAction(selector, value string) *InputElementAction {
	return &InputElementAction{
		Selector:       selector,
		Value:          value,
		ClearFirst:     true,
		ClickFirst:     true,
		WaitTimeout:    5 * time.Second,
		TakeScreenshot: false,
		ScreenshotName: "input_completed",
	}
}

// WithClearFirst 设置是否先清空输入框
func (a *InputElementAction) WithClearFirst(clear bool) *InputElementAction {
	a.ClearFirst = clear
	return a
}

// WithClickFirst 设置是否先点击输入框
func (a *InputElementAction) WithClickFirst(click bool) *InputElementAction {
	a.ClickFirst = click
	return a
}

// WithTimeout 设置等待超时时间
func (a *InputElementAction) WithTimeout(timeout time.Duration) *InputElementAction {
	a.WaitTimeout = timeout
	return a
}

// WithScreenshot 设置截图选项
func (a *InputElementAction) WithScreenshot(take bool, name string) *InputElementAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// Execute 执行输入操作
func (a *InputElementAction) Execute(ctx *ActionContext) error {
	logger.Debugf("执行输入操作，选择器: %s，值: %s", a.Selector, a.Value)

	// 等待元素出现
	if err := ctx.Manager.WaitForElement(a.Selector, a.WaitTimeout); err != nil {
		ctx.Manager.TakeScreenshot("input_element_not_found")
		return fmt.Errorf("未找到输入元素 %s: %w", a.Selector, err)
	}

	// 点击输入框
	if a.ClickFirst {
		if err := ctx.Manager.Click(a.Selector); err != nil {
			return fmt.Errorf("点击输入框失败: %w", err)
		}
		HumanLikeDelay()
	}

	// 清空输入框
	if a.ClearFirst {
		if err := a.clearInput(ctx); err != nil {
			logger.Warnf("清空输入框失败: %v", err)
		}
	}

	// 输入值
	if err := ctx.Manager.SendKeys(a.Selector, a.Value); err != nil {
		return fmt.Errorf("输入失败: %w", err)
	}

	logger.Debugf("输入完成: %s", a.Value)
	HumanLikeDelay()

	// 截图
	if a.TakeScreenshot {
		if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
			logger.Warnf("截图失败: %v", err)
		}
	}

	return nil
}

// clearInput 清空输入框内容
func (a *InputElementAction) clearInput(ctx *ActionContext) error {
	script := fmt.Sprintf(`
		(function() {
			const element = document.querySelector('%s');
			if (element) {
				element.value = '';
				element.dispatchEvent(new Event('input', { bubbles: true }));
				element.dispatchEvent(new Event('change', { bubbles: true }));
				return true;
			}
			return false;
		})();
	`, a.Selector)

	var result bool
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("执行清空脚本失败: %w", err)
	}

	if !result {
		return fmt.Errorf("未找到要清空的元素")
	}

	return nil
}

// GetName 获取操作名称
func (a *InputElementAction) GetName() string {
	return "InputElement"
}

// InputWithSelectorsAction 使用选择器列表的输入操作
type InputWithSelectorsAction struct {
	Selectors      ElementSelectors
	Value          string
	ClearFirst     bool
	ClickFirst     bool
	WaitTimeout    time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewInputWithSelectorsAction 创建使用选择器列表的输入操作
func NewInputWithSelectorsAction(selectors ElementSelectors, value string) *InputWithSelectorsAction {
	return &InputWithSelectorsAction{
		Selectors:      selectors,
		Value:          value,
		ClearFirst:     true,
		ClickFirst:     true,
		WaitTimeout:    5 * time.Second,
		TakeScreenshot: false,
		ScreenshotName: "input_completed",
	}
}

// WithClearFirst 设置是否先清空输入框
func (a *InputWithSelectorsAction) WithClearFirst(clear bool) *InputWithSelectorsAction {
	a.ClearFirst = clear
	return a
}

// WithClickFirst 设置是否先点击输入框
func (a *InputWithSelectorsAction) WithClickFirst(click bool) *InputWithSelectorsAction {
	a.ClickFirst = click
	return a
}

// WithTimeout 设置等待超时时间
func (a *InputWithSelectorsAction) WithTimeout(timeout time.Duration) *InputWithSelectorsAction {
	a.WaitTimeout = timeout
	return a
}

// WithScreenshot 设置截图选项
func (a *InputWithSelectorsAction) WithScreenshot(take bool, name string) *InputWithSelectorsAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// Execute 执行输入操作
func (a *InputWithSelectorsAction) Execute(ctx *ActionContext) error {
	logger.Debugf("执行输入操作，值: %s", a.Value)

	// 查找输入框
	selector, err := a.Selectors.FindElement(ctx, a.WaitTimeout)
	if err != nil {
		ctx.Manager.TakeScreenshot("input_element_not_found")
		return fmt.Errorf("未找到输入框: %w", err)
	}

	logger.Debugf("找到输入框: %s", selector)

	// 使用找到的选择器创建基础输入操作
	inputAction := NewInputElementAction(selector, a.Value).
		WithClearFirst(a.ClearFirst).
		WithClickFirst(a.ClickFirst).
		WithScreenshot(a.TakeScreenshot, a.ScreenshotName)

	return inputAction.Execute(ctx)
}

// GetName 获取操作名称
func (a *InputWithSelectorsAction) GetName() string {
	return "InputWithSelectors"
}
