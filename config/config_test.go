package config

import (
	"os"
	"testing"
)

func TestConfig(t *testing.T) {
	// 保存原始环境变量
	originalEnv := make(map[string]string)
	envVars := []string{
		"DOMAIN", "TEMP_MAIL", "TEMP_MAIL_EPIN", "IMAP_SERVER", 
		"IMAP_USER", "IMAP_PASS", "BROWSER_HEADLESS",
	}
	
	for _, key := range envVars {
		originalEnv[key] = os.Getenv(key)
	}
	
	// 测试完成后恢复环境变量
	defer func() {
		for key, value := range originalEnv {
			if value == "" {
				os.Unsetenv(key)
			} else {
				os.Setenv(key, value)
			}
		}
	}()

	t.Run("ValidTempMailConfig", func(t *testing.T) {
		// 设置临时邮箱配置
		os.Setenv("DOMAIN", "example.com")
		os.Setenv("TEMP_MAIL", "testuser")
		os.Setenv("TEMP_MAIL_EPIN", "123456")
		os.Unsetenv("IMAP_SERVER")
		os.Unsetenv("IMAP_USER")
		os.Unsetenv("IMAP_PASS")

		config, err := LoadConfig()
		if err != nil {
			t.Fatalf("加载配置失败: %v", err)
		}

		if config.Domain != "example.com" {
			t.Errorf("域名配置错误，期望: example.com，实际: %s", config.Domain)
		}

		if config.IsIMAPMode() {
			t.Error("应该是临时邮箱模式，但检测为IMAP模式")
		}
	})

	t.Run("ValidIMAPConfig", func(t *testing.T) {
		// 设置IMAP配置
		os.Setenv("DOMAIN", "example.com")
		os.Setenv("TEMP_MAIL", "null")
		os.Setenv("IMAP_SERVER", "imap.example.com")
		os.Setenv("IMAP_USER", "<EMAIL>")
		os.Setenv("IMAP_PASS", "password123")

		config, err := LoadConfig()
		if err != nil {
			t.Fatalf("加载配置失败: %v", err)
		}

		if !config.IsIMAPMode() {
			t.Error("应该是IMAP模式，但检测为临时邮箱模式")
		}

		if config.IMAPServer != "imap.example.com" {
			t.Errorf("IMAP服务器配置错误，期望: imap.example.com，实际: %s", config.IMAPServer)
		}
	})

	t.Run("MissingDomain", func(t *testing.T) {
		// 清除域名配置
		os.Unsetenv("DOMAIN")
		os.Setenv("TEMP_MAIL", "testuser")
		os.Setenv("TEMP_MAIL_EPIN", "123456")

		_, err := LoadConfig()
		if err == nil {
			t.Error("应该因为缺少域名配置而失败")
		}
	})

	t.Run("MissingTempMailConfig", func(t *testing.T) {
		// 设置域名但缺少临时邮箱配置
		os.Setenv("DOMAIN", "example.com")
		os.Setenv("TEMP_MAIL", "testuser")
		os.Unsetenv("TEMP_MAIL_EPIN")

		_, err := LoadConfig()
		if err == nil {
			t.Error("应该因为缺少临时邮箱EPIN而失败")
		}
	})

	t.Run("MissingIMAPConfig", func(t *testing.T) {
		// 设置IMAP模式但缺少配置
		os.Setenv("DOMAIN", "example.com")
		os.Setenv("TEMP_MAIL", "null")
		os.Setenv("IMAP_SERVER", "imap.example.com")
		os.Unsetenv("IMAP_USER")
		os.Unsetenv("IMAP_PASS")

		_, err := LoadConfig()
		if err == nil {
			t.Error("应该因为缺少IMAP用户配置而失败")
		}
	})

	t.Run("DefaultValues", func(t *testing.T) {
		// 设置最小配置
		os.Setenv("DOMAIN", "example.com")
		os.Setenv("TEMP_MAIL", "testuser")
		os.Setenv("TEMP_MAIL_EPIN", "123456")
		os.Unsetenv("BROWSER_HEADLESS")
		os.Unsetenv("MAX_RETRIES")

		config, err := LoadConfig()
		if err != nil {
			t.Fatalf("加载配置失败: %v", err)
		}

		// 检查默认值
		if !config.BrowserHeadless {
			t.Error("浏览器无头模式默认值应该为true")
		}

		if config.MaxRetries != 3 {
			t.Errorf("最大重试次数默认值应该为3，实际: %d", config.MaxRetries)
		}

		if config.IMAPPort != 993 {
			t.Errorf("IMAP端口默认值应该为993，实际: %d", config.IMAPPort)
		}
	})
}

func TestConfigValidation(t *testing.T) {
	t.Run("ValidateEmptyDomain", func(t *testing.T) {
		config := &Config{
			Domain:       "",
			TempMail:     "testuser",
			TempMailEpin: "123456",
		}

		err := config.Validate()
		if err == nil {
			t.Error("应该因为域名为空而验证失败")
		}
	})

	t.Run("ValidateTempMailMode", func(t *testing.T) {
		config := &Config{
			Domain:       "example.com",
			TempMail:     "testuser",
			TempMailEpin: "123456",
		}

		err := config.Validate()
		if err != nil {
			t.Errorf("临时邮箱模式验证失败: %v", err)
		}
	})

	t.Run("ValidateIMAPMode", func(t *testing.T) {
		config := &Config{
			Domain:     "example.com",
			TempMail:   "null",
			IMAPServer: "imap.example.com",
			IMAPUser:   "<EMAIL>",
			IMAPPass:   "password123",
		}

		err := config.Validate()
		if err != nil {
			t.Errorf("IMAP模式验证失败: %v", err)
		}
	})
}
