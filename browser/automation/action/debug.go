package action

import (
	"fmt"
	"os"
	"time"

	"auto-register/logger"
)

// ScreenshotAction 截图操作
type ScreenshotAction struct {
	Filename string
	Required bool
}

// NewScreenshotAction 创建截图操作
func NewScreenshotAction(filename string) *ScreenshotAction {
	return &ScreenshotAction{
		Filename: filename,
		Required: false,
	}
}

// WithRequired 设置是否必需成功
func (a *ScreenshotAction) WithRequired(required bool) *ScreenshotAction {
	a.Required = required
	return a
}

// Execute 执行截图操作
func (a *ScreenshotAction) Execute(ctx *ActionContext) error {
	logger.Debugf("执行截图: %s", a.Filename)

	if err := ctx.Manager.TakeScreenshot(a.Filename); err != nil {
		if a.Required {
			return fmt.Errorf("截图失败: %w", err)
		} else {
			logger.Warnf("截图失败: %v", err)
		}
	}

	return nil
}

// GetName 获取操作名称
func (a *ScreenshotAction) GetName() string {
	return "Screenshot"
}

// SavePageHTMLAction 保存页面HTML操作
type SavePageHTMLAction struct {
	Filename string
	Required bool
}

// NewSavePageHTMLAction 创建保存页面HTML操作
func NewSavePageHTMLAction(filename string) *SavePageHTMLAction {
	return &SavePageHTMLAction{
		Filename: filename,
		Required: false,
	}
}

// WithRequired 设置是否必需成功
func (a *SavePageHTMLAction) WithRequired(required bool) *SavePageHTMLAction {
	a.Required = required
	return a
}

// Execute 执行保存页面HTML操作
func (a *SavePageHTMLAction) Execute(ctx *ActionContext) error {
	logger.Debugf("保存页面HTML: %s", a.Filename)

	script := `return document.documentElement.outerHTML;`

	var html string
	if err := ctx.Manager.ExecuteScript(script, &html); err != nil {
		if a.Required {
			return fmt.Errorf("获取页面HTML失败: %w", err)
		} else {
			logger.Warnf("获取页面HTML失败: %v", err)
			return nil
		}
	}

	// 创建调试目录
	debugDir := "debug"
	if err := os.MkdirAll(debugDir, 0755); err != nil {
		if a.Required {
			return fmt.Errorf("创建调试目录失败: %w", err)
		} else {
			logger.Warnf("创建调试目录失败: %v", err)
			return nil
		}
	}

	// 保存HTML文件
	htmlFile := fmt.Sprintf("%s/%s.html", debugDir, a.Filename)
	if err := os.WriteFile(htmlFile, []byte(html), 0644); err != nil {
		if a.Required {
			return fmt.Errorf("保存HTML文件失败: %w", err)
		} else {
			logger.Warnf("保存HTML文件失败: %v", err)
			return nil
		}
	}

	logger.Infof("页面HTML已保存: %s", htmlFile)
	return nil
}

// GetName 获取操作名称
func (a *SavePageHTMLAction) GetName() string {
	return "SavePageHTML"
}

// DelayAction 延迟操作
type DelayAction struct {
	Duration    time.Duration
	Description string
	Random      bool
	MinDuration time.Duration
	MaxDuration time.Duration
}

// NewDelayAction 创建延迟操作
func NewDelayAction(duration time.Duration) *DelayAction {
	return &DelayAction{
		Duration:    duration,
		Description: "等待",
		Random:      false,
	}
}

// NewRandomDelayAction 创建随机延迟操作
func NewRandomDelayAction(min, max time.Duration) *DelayAction {
	return &DelayAction{
		MinDuration: min,
		MaxDuration: max,
		Description: "随机等待",
		Random:      true,
	}
}

// NewHumanLikeDelayAction 创建人性化延迟操作
func NewHumanLikeDelayAction() *DelayAction {
	return &DelayAction{
		MinDuration: 1 * time.Second,
		MaxDuration: 3 * time.Second,
		Description: "人性化等待",
		Random:      true,
	}
}

// WithDescription 设置描述
func (a *DelayAction) WithDescription(desc string) *DelayAction {
	a.Description = desc
	return a
}

// Execute 执行延迟操作
func (a *DelayAction) Execute(ctx *ActionContext) error {
	var duration time.Duration

	if a.Random {
		// 随机延迟
		diff := a.MaxDuration - a.MinDuration
		randomNanos := time.Now().UnixNano() % int64(diff)
		duration = a.MinDuration + time.Duration(randomNanos)
	} else {
		duration = a.Duration
	}

	logger.Debugf("%s: %v", a.Description, duration)
	time.Sleep(duration)
	return nil
}

// GetName 获取操作名称
func (a *DelayAction) GetName() string {
	return "Delay"
}

// LogAction 日志操作
type LogAction struct {
	Message string
	Level   string // INFO, DEBUG, WARN, ERROR
}

// NewLogAction 创建日志操作
func NewLogAction(message string) *LogAction {
	return &LogAction{
		Message: message,
		Level:   "INFO",
	}
}

// WithLevel 设置日志级别
func (a *LogAction) WithLevel(level string) *LogAction {
	a.Level = level
	return a
}

// Execute 执行日志操作
func (a *LogAction) Execute(ctx *ActionContext) error {
	switch a.Level {
	case "DEBUG":
		logger.Debug(a.Message)
	case "WARN":
		logger.Warn(a.Message)
	case "ERROR":
		logger.Error(a.Message)
	default:
		logger.Info(a.Message)
	}
	return nil
}

// GetName 获取操作名称
func (a *LogAction) GetName() string {
	return "Log"
}

// ExecuteScriptAction 执行脚本操作
type ExecuteScriptAction struct {
	Script      string
	StoreResult bool
	ResultKey   string
	Required    bool
	Description string
}

// NewExecuteScriptAction 创建执行脚本操作
func NewExecuteScriptAction(script string) *ExecuteScriptAction {
	return &ExecuteScriptAction{
		Script:      script,
		StoreResult: false,
		Required:    false,
		Description: "执行JavaScript脚本",
	}
}

// WithStoreResult 设置存储结果
func (a *ExecuteScriptAction) WithStoreResult(key string) *ExecuteScriptAction {
	a.StoreResult = true
	a.ResultKey = key
	return a
}

// WithRequired 设置是否必需成功
func (a *ExecuteScriptAction) WithRequired(required bool) *ExecuteScriptAction {
	a.Required = required
	return a
}

// WithDescription 设置描述
func (a *ExecuteScriptAction) WithDescription(desc string) *ExecuteScriptAction {
	a.Description = desc
	return a
}

// Execute 执行脚本操作
func (a *ExecuteScriptAction) Execute(ctx *ActionContext) error {
	logger.Debugf("%s", a.Description)

	var result interface{}
	if err := ctx.Manager.ExecuteScript(a.Script, &result); err != nil {
		if a.Required {
			return fmt.Errorf("执行脚本失败: %w", err)
		} else {
			logger.Warnf("执行脚本失败: %v", err)
			return nil
		}
	}

	if a.StoreResult {
		// 这里可以扩展为将结果存储到上下文中
		logger.Debugf("脚本执行结果: %v", result)
	}

	return nil
}

// GetName 获取操作名称
func (a *ExecuteScriptAction) GetName() string {
	return "ExecuteScript"
}
