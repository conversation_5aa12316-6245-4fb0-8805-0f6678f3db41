package flow

import (
	"fmt"
	"time"

	"auto-register/browser/automation/action"
	"auto-register/browser/automation/step"
	"auto-register/generator"
	"auto-register/logger"
)

// CursorRegisterFlow Cursor注册流程
type CursorRegisterFlow struct {
	BaseURL string
}

// NewCursorRegisterFlow 创建Cursor注册流程
func NewCursorRegisterFlow() *CursorRegisterFlow {
	return &CursorRegisterFlow{
		BaseURL: "https://cursor.sh/",
	}
}

// WithBaseURL 设置基础URL
func (f *CursorRegisterFlow) WithBaseURL(url string) *CursorRegisterFlow {
	f.BaseURL = url
	return f
}

// RegisterWithAccount 使用账号信息注册
func (f *CursorRegisterFlow) RegisterWithAccount(ctx *action.ActionContext, account *generator.AccountInfo) error {
	logger.Info("开始Cursor注册流程")

	// 创建步骤序列
	sequence := step.NewStepSequence("CursorRegister").
		Add(step.NewNavigateToRegisterPageStep(f.BaseURL).
			WithScreenshot(true, "cursor_register_page")).
		Add(step.NewWaitForPageLoadStep()).
		Add(step.NewFillEmailStep(account.Email).
			WithScreenshot(true, "cursor_email_filled")).
		Add(step.NewFillUsernameStep(account.Email).
			WithScreenshot(true, "cursor_username_filled")).
		Add(step.NewFillPasswordStep(account.Password).
			WithScreenshot(true, "cursor_password_filled")).
		Add(step.NewFillFirstNameStep(account.FirstName).
			WithScreenshot(true, "cursor_firstname_filled")).
		Add(step.NewFillLastNameStep(account.LastName).
			WithScreenshot(true, "cursor_lastname_filled")).
		Add(step.NewHandleTurnstileStep().WithRequired(false)).
		Add(step.NewClickRegisterStep()).
		Add(NewVerifyRegistrationResultStep())

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Cursor注册流程失败: %w", err)
	}

	logger.Info("Cursor注册流程完成")
	return nil
}

// QuickRegister 快速注册（最小步骤）
func (f *CursorRegisterFlow) QuickRegister(ctx *action.ActionContext, account *generator.AccountInfo) error {
	logger.Info("开始Cursor快速注册")

	sequence := step.NewStepSequence("CursorQuickRegister").
		Add(step.NewNavigateToRegisterPageStep(f.BaseURL)).
		Add(step.NewFillEmailStep(account.Email)).
		Add(step.NewFillPasswordStep(account.Password)).
		Add(step.NewClickRegisterStep()).
		Add(NewVerifyRegistrationResultStep())

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Cursor快速注册失败: %w", err)
	}

	logger.Info("Cursor快速注册完成")
	return nil
}

// DebugRegister 调试注册（包含详细调试信息）
func (f *CursorRegisterFlow) DebugRegister(ctx *action.ActionContext, account *generator.AccountInfo) error {
	logger.Info("开始Cursor调试注册")

	sequence := step.NewStepSequence("CursorDebugRegister").
		Add(&LogStep{Message: "=== 开始Cursor调试注册流程 ==="}).
		Add(step.NewNavigateToRegisterPageStep(f.BaseURL).
			WithScreenshot(true, "debug_01_register_page")).
		Add(&ScreenshotStep{Name: "debug_02_page_loaded"}).
		Add(&SavePageHTMLStep{Name: "debug_01_register_page"}).
		Add(step.NewWaitForPageLoadStep()).
		Add(step.NewFillEmailStep(account.Email).
			WithScreenshot(true, "debug_03_email_filled")).
		Add(&DelayStep{Duration: 2000, Description: "等待邮箱输入稳定"}).
		Add(step.NewFillUsernameStep(account.Email).
			WithScreenshot(true, "debug_04_username_filled")).
		Add(&DelayStep{Duration: 2000, Description: "等待用户名输入稳定"}).
		Add(step.NewFillPasswordStep(account.Password).
			WithScreenshot(true, "debug_05_password_filled")).
		Add(&DelayStep{Duration: 2000, Description: "等待密码输入稳定"}).
		Add(step.NewFillFirstNameStep(account.FirstName).
			WithScreenshot(true, "debug_06_firstname_filled")).
		Add(&DelayStep{Duration: 2000, Description: "等待名字输入稳定"}).
		Add(step.NewFillLastNameStep(account.LastName).
			WithScreenshot(true, "debug_07_lastname_filled")).
		Add(&DelayStep{Duration: 2000, Description: "等待姓氏输入稳定"}).
		Add(&SavePageHTMLStep{Name: "debug_02_before_submit"}).
		Add(step.NewHandleTurnstileStep().WithRequired(false)).
		Add(step.NewClickRegisterStep().
			WithScreenshot(true, "debug_08_register_clicked")).
		Add(&DelayStep{Duration: 5000, Description: "等待注册处理"}).
		Add(&SavePageHTMLStep{Name: "debug_03_after_register"}).
		Add(NewVerifyRegistrationResultStep()).
		Add(&LogStep{Message: "=== Cursor调试注册流程完成 ==="})

	if err := sequence.Execute(ctx); err != nil {
		return fmt.Errorf("Cursor调试注册失败: %w", err)
	}

	logger.Info("Cursor调试注册完成")
	return nil
}

// GetName 获取流程名称
func (f *CursorRegisterFlow) GetName() string {
	return "CursorRegister"
}

// VerifyRegistrationResultStep 验证注册结果步骤
type VerifyRegistrationResultStep struct {
	WaitTime           time.Duration
	SuccessURLs        []string
	SuccessTitles      []string
	MaxRetries         int
	RetryInterval      time.Duration
	AllowUnknownStatus bool
	TakeScreenshot     bool
}

// NewVerifyRegistrationResultStep 创建验证注册结果步骤
func NewVerifyRegistrationResultStep() *VerifyRegistrationResultStep {
	return &VerifyRegistrationResultStep{
		WaitTime: 5 * time.Second,
		SuccessURLs: []string{
			"cursor.sh",
			"dashboard",
			"welcome",
			"success",
		},
		SuccessTitles: []string{
			"dashboard", "welcome", "success", "cursor",
		},
		MaxRetries:         2,
		RetryInterval:      3 * time.Second,
		AllowUnknownStatus: true,
		TakeScreenshot:     true,
	}
}

// WithWaitTime 设置等待时间
func (s *VerifyRegistrationResultStep) WithWaitTime(duration time.Duration) *VerifyRegistrationResultStep {
	s.WaitTime = duration
	return s
}

// WithSuccessURLs 设置成功URL模式
func (s *VerifyRegistrationResultStep) WithSuccessURLs(urls []string) *VerifyRegistrationResultStep {
	s.SuccessURLs = urls
	return s
}

// WithSuccessTitles 设置成功标题模式
func (s *VerifyRegistrationResultStep) WithSuccessTitles(titles []string) *VerifyRegistrationResultStep {
	s.SuccessTitles = titles
	return s
}

// WithRetries 设置重试选项
func (s *VerifyRegistrationResultStep) WithRetries(maxRetries int, interval time.Duration) *VerifyRegistrationResultStep {
	s.MaxRetries = maxRetries
	s.RetryInterval = interval
	return s
}

// WithAllowUnknownStatus 设置是否允许未知状态
func (s *VerifyRegistrationResultStep) WithAllowUnknownStatus(allow bool) *VerifyRegistrationResultStep {
	s.AllowUnknownStatus = allow
	return s
}

// WithScreenshot 设置截图选项
func (s *VerifyRegistrationResultStep) WithScreenshot(take bool) *VerifyRegistrationResultStep {
	s.TakeScreenshot = take
	return s
}

// Execute 执行验证注册结果步骤
func (s *VerifyRegistrationResultStep) Execute(ctx *action.ActionContext) error {
	logger.Info("执行验证注册结果步骤")

	// 复用登录验证逻辑，但调整成功条件
	verifyAction := action.NewVerifyLoginResultAction().
		WithWaitTime(s.WaitTime).
		WithSuccessURLs(s.SuccessURLs).
		WithSuccessTitles(s.SuccessTitles).
		WithRetries(s.MaxRetries, s.RetryInterval).
		WithAllowUnknownStatus(s.AllowUnknownStatus)

	if err := verifyAction.Execute(ctx); err != nil {
		return fmt.Errorf("验证注册结果失败: %w", err)
	}

	logger.Info("注册结果验证完成")
	return nil
}

// GetName 获取步骤名称
func (s *VerifyRegistrationResultStep) GetName() string {
	return "VerifyRegistrationResult"
}
