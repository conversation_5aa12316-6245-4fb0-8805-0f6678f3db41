package email

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"

	"auto-register/config"
	"auto-register/logger"
)

// TempMailHandler 临时邮箱处理器
type TempMailHandler struct {
	config  *config.Config
	account string
	client  *http.Client
}

// TempMailResponse 临时邮箱API响应
type TempMailResponse struct {
	Result  bool   `json:"result"`
	FirstID string `json:"first_id"`
}

// MailDetail 邮件详情
type MailDetail struct {
	Result  bool   `json:"result"`
	Text    string `json:"text"`
	Subject string `json:"subject"`
}

// NewTempMailHandler 创建临时邮箱处理器
func NewTempMailHandler(cfg *config.Config, account string) *TempMailHandler {
	return &TempMailHandler{
		config:  cfg,
		account: account,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetVerificationCode 获取验证码
func (th *TempMailHandler) GetVerificationCode(maxRetries int, retryInterval time.Duration) (string, error) {
	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			time.Sleep(retryInterval)
		}

		code, err := th.fetchVerificationCode()
		if err == nil && code != "" {
			return code, nil
		}

		logger.Debugf("临时邮箱获取验证码失败 (尝试 %d/%d): %v", attempt+1, maxRetries, err)
	}

	return "", fmt.Errorf("临时邮箱获取验证码失败，已达最大重试次数")
}

// fetchVerificationCode 从临时邮箱获取验证码
func (th *TempMailHandler) fetchVerificationCode() (string, error) {
	// 获取邮件列表
	mailList, err := th.getMailList()
	if err != nil {
		return "", fmt.Errorf("获取邮件列表失败: %w", err)
	}

	if !mailList.Result || mailList.FirstID == "" {
		return "", fmt.Errorf("没有新邮件")
	}

	// 获取邮件详情
	mailDetail, err := th.getMailDetail(mailList.FirstID)
	if err != nil {
		return "", fmt.Errorf("获取邮件详情失败: %w", err)
	}

	if !mailDetail.Result {
		return "", fmt.Errorf("邮件详情获取失败")
	}

	logger.Infof("找到邮件主题: %s", mailDetail.Subject)

	// 提取验证码
	code := th.extractVerificationCode(mailDetail.Text)
	if code == "" {
		return "", fmt.Errorf("未找到验证码")
	}

	// 删除邮件
	if err := th.deleteMail(mailList.FirstID); err != nil {
		logger.Warnf("删除邮件失败: %v", err)
	}

	return code, nil
}

// getMailList 获取邮件列表
func (th *TempMailHandler) getMailList() (*TempMailResponse, error) {
	username := strings.Split(th.config.TempMail, "@")[0]
	url := fmt.Sprintf("https://tempmail.plus/api/mails?email=%s%s&limit=20&epin=%s",
		username, th.config.TempMailExt, th.config.TempMailEpin)

	resp, err := th.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var mailResp TempMailResponse
	if err := json.Unmarshal(body, &mailResp); err != nil {
		return nil, err
	}

	return &mailResp, nil
}

// getMailDetail 获取邮件详情
func (th *TempMailHandler) getMailDetail(mailID string) (*MailDetail, error) {
	username := strings.Split(th.config.TempMail, "@")[0]
	url := fmt.Sprintf("https://tempmail.plus/api/mails/%s?email=%s%s&epin=%s",
		mailID, username, th.config.TempMailExt, th.config.TempMailEpin)

	resp, err := th.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var detail MailDetail
	if err := json.Unmarshal(body, &detail); err != nil {
		return nil, err
	}

	return &detail, nil
}

// deleteMail 删除邮件
func (th *TempMailHandler) deleteMail(mailID string) error {
	username := strings.Split(th.config.TempMail, "@")[0]
	url := fmt.Sprintf("https://tempmail.plus/api/mails/%s?email=%s%s&epin=%s",
		mailID, username, th.config.TempMailExt, th.config.TempMailEpin)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return err
	}

	resp, err := th.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("删除邮件失败，HTTP错误: %d", resp.StatusCode)
	}

	return nil
}

// extractVerificationCode 提取验证码
func (th *TempMailHandler) extractVerificationCode(text string) string {
	// 移除邮箱地址，避免域名中的数字被误识别
	text = strings.ReplaceAll(text, th.account, "")

	// 查找6位数字验证码，确保不紧跟在字母或域名相关符号后面
	re := regexp.MustCompile(`(?<![a-zA-Z.])\b\d{6}\b(?![a-zA-Z.])`)
	matches := re.FindAllString(text, -1)

	if len(matches) > 0 {
		return matches[0]
	}

	return ""
}
