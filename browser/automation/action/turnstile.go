package action

import (
	"fmt"
	"math/rand"
	"time"

	"auto-register/logger"
)

// TurnstileAction Turnstile验证操作
type TurnstileAction struct {
	WaitTimeout    time.Duration
	MaxRetries     int
	RetryInterval  time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewTurnstileAction 创建Turnstile验证操作
func NewTurnstileAction() *TurnstileAction {
	return &TurnstileAction{
		WaitTimeout:    30 * time.Second,
		MaxRetries:     3,
		RetryInterval:  5 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "turnstile_completed",
	}
}

// WithTimeout 设置等待超时时间
func (a *TurnstileAction) WithTimeout(timeout time.Duration) *TurnstileAction {
	a.WaitTimeout = timeout
	return a
}

// WithRetries 设置重试选项
func (a *TurnstileAction) WithRetries(maxRetries int, interval time.Duration) *TurnstileAction {
	a.MaxRetries = maxRetries
	a.RetryInterval = interval
	return a
}

// WithScreenshot 设置截图选项
func (a *TurnstileAction) WithScreenshot(take bool, name string) *TurnstileAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// Execute 执行Turnstile验证操作
func (a *TurnstileAction) Execute(ctx *ActionContext) error {
	logger.Info("检测Turnstile验证")

	// 保存开始截图
	if a.TakeScreenshot {
		if err := ctx.Manager.TakeScreenshot("turnstile_start"); err != nil {
			logger.Warnf("保存开始截图失败: %v", err)
		}
	}

	retryCount := 0
	for retryCount < a.MaxRetries {
		retryCount++
		logger.Debugf("Turnstile验证尝试 %d/%d", retryCount, a.MaxRetries)

		// 尝试处理Turnstile验证
		if err := a.handleTurnstileVerification(ctx); err != nil {
			logger.Debugf("当前尝试失败: %v", err)

			// 检查是否已经验证成功
			if a.checkVerificationSuccess(ctx) {
				logger.Info("Turnstile验证已完成")
				if a.TakeScreenshot {
					ctx.Manager.TakeScreenshot("turnstile_success")
				}
				return nil
			}

			// 如果不是最后一次重试，等待后继续
			if retryCount < a.MaxRetries {
				// 随机延迟1-2秒
				delay := time.Duration(1000+rand.Intn(1000)) * time.Millisecond
				time.Sleep(delay)
				continue
			}
		} else {
			// 验证成功
			logger.Info("Turnstile验证通过")
			if a.TakeScreenshot {
				ctx.Manager.TakeScreenshot("turnstile_success")
			}
			return nil
		}
	}

	// 超过最大重试次数
	logger.Error("Turnstile验证失败，已达最大重试次数")
	if a.TakeScreenshot {
		ctx.Manager.TakeScreenshot("turnstile_failed")
	}
	return fmt.Errorf("turnstile验证失败，已达最大重试次数")
}

// handleTurnstileVerification 处理Turnstile验证
func (a *TurnstileAction) handleTurnstileVerification(ctx *ActionContext) error {
	// 查找Turnstile验证框架元素
	script := `
		try {
			// 查找cf-turnstile元素
			const turnstileElement = document.querySelector('#cf-turnstile');
			if (!turnstileElement) {
				return { found: false, message: '未找到cf-turnstile元素' };
			}

			// 获取shadow root
			const shadowRoot = turnstileElement.shadowRoot;
			if (!shadowRoot) {
				return { found: false, message: '未找到shadow root' };
			}

			// 查找iframe
			const iframe = shadowRoot.querySelector('iframe');
			if (!iframe) {
				return { found: false, message: '未找到iframe' };
			}

			// 尝试访问iframe内容
			try {
				const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
				const inputElement = iframeDoc.querySelector('input[type="checkbox"]');
				if (inputElement) {
					return { found: true, element: 'input', message: '找到验证输入框' };
				}
			} catch (e) {
				// 跨域限制，这是正常的
				return { found: true, element: 'iframe', message: '找到iframe但无法访问内容' };
			}

			return { found: true, element: 'turnstile', message: '找到Turnstile元素' };
		} catch (e) {
			return { found: false, message: '脚本执行错误: ' + e.message };
		}
	`

	var result map[string]interface{}
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("执行Turnstile检测脚本失败: %w", err)
	}

	found, ok := result["found"].(bool)
	if !ok || !found {
		message := result["message"].(string)
		return fmt.Errorf("未找到Turnstile验证元素: %s", message)
	}

	logger.Info("检测到Turnstile验证")

	// 随机延迟1-3秒模拟人类行为
	delay := time.Duration(1000+rand.Intn(2000)) * time.Millisecond
	time.Sleep(delay)

	// 尝试点击验证框
	clickScript := `
		try {
			const turnstileElement = document.querySelector('#cf-turnstile');
			if (turnstileElement && turnstileElement.shadowRoot) {
				const iframe = turnstileElement.shadowRoot.querySelector('iframe');
				if (iframe) {
					try {
						const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
						const inputElement = iframeDoc.querySelector('input[type="checkbox"]');
						if (inputElement) {
							inputElement.click();
							return { clicked: true, message: '成功点击验证框' };
						}
					} catch (e) {
						// 跨域限制，尝试点击iframe
						iframe.click();
						return { clicked: true, message: '点击了iframe' };
					}
				}
			}
			return { clicked: false, message: '无法点击验证元素' };
		} catch (e) {
			return { clicked: false, message: '点击脚本错误: ' + e.message };
		}
	`

	var clickResult map[string]interface{}
	if err := ctx.Manager.ExecuteScript(clickScript, &clickResult); err != nil {
		logger.Warnf("执行点击脚本失败: %v", err)
	} else {
		clicked, _ := clickResult["clicked"].(bool)
		message, _ := clickResult["message"].(string)
		if clicked {
			logger.Info("Turnstile验证框点击成功")
		} else {
			logger.Debugf("点击失败: %s", message)
		}
	}

	// 等待2秒让验证处理
	time.Sleep(2 * time.Second)

	// 保存点击后截图
	if a.TakeScreenshot {
		ctx.Manager.TakeScreenshot("turnstile_clicked")
	}

	return nil
}

// checkVerificationSuccess 检查验证是否成功
func (a *TurnstileAction) checkVerificationSuccess(ctx *ActionContext) bool {
	// 检查页面是否有成功标识
	successSelectors := []string{
		`@name=password`,                     // 密码页面
		`@data-index=0`,                      // 验证码页面
		`Account Settings`,                   // 账户设置页面
		`.cf-turnstile-success`,              // Turnstile成功标识
		`[data-cf-turnstile-success="true"]`, // Turnstile成功属性
	}

	for _, selector := range successSelectors {
		if ctx.Manager.IsElementPresent(selector) {
			logger.Debugf("检测到成功标识: %s", selector)
			return true
		}
	}

	// 使用JavaScript检查Turnstile状态
	script := `
		try {
			// 检查是否有成功元素
			const successElements = document.querySelectorAll('.cf-turnstile-success, [data-cf-turnstile-success="true"]');
			if (successElements.length > 0) {
				return { success: true, reason: 'found_success_elements' };
			}

			// 检查Turnstile元素状态
			const turnstileElement = document.querySelector('#cf-turnstile');
			if (turnstileElement) {
				// 检查是否有成功类名
				if (turnstileElement.classList.contains('cf-turnstile-success')) {
					return { success: true, reason: 'turnstile_success_class' };
				}

				// 检查data属性
				if (turnstileElement.getAttribute('data-cf-turnstile-success') === 'true') {
					return { success: true, reason: 'turnstile_success_attribute' };
				}
			}

			// 检查页面URL变化
			if (window.location.href.includes('password') ||
				window.location.href.includes('verify') ||
				window.location.href.includes('settings')) {
				return { success: true, reason: 'url_changed' };
			}

			// 检查特定页面元素
			if (document.querySelector('[name="password"]') ||
				document.querySelector('[data-index="0"]') ||
				document.querySelector('h1, h2, h3').textContent.includes('Account Settings')) {
				return { success: true, reason: 'page_elements' };
			}

			return { success: false, reason: 'no_success_indicators' };
		} catch (e) {
			return { success: false, reason: 'script_error: ' + e.message };
		}
	`

	var result map[string]interface{}
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		logger.Debugf("执行成功检查脚本失败: %v", err)
		return false
	}

	success, ok := result["success"].(bool)
	if ok && success {
		reason, _ := result["reason"].(string)
		logger.Infof("验证成功，原因: %s", reason)
		return true
	}

	return false
}

// GetName 获取操作名称
func (a *TurnstileAction) GetName() string {
	return "Turnstile"
}

// WaitForTurnstileAction 等待Turnstile出现操作
type WaitForTurnstileAction struct {
	WaitTimeout time.Duration
	Required    bool
}

// NewWaitForTurnstileAction 创建等待Turnstile出现操作
func NewWaitForTurnstileAction() *WaitForTurnstileAction {
	return &WaitForTurnstileAction{
		WaitTimeout: 10 * time.Second,
		Required:    false,
	}
}

// WithTimeout 设置超时时间
func (a *WaitForTurnstileAction) WithTimeout(timeout time.Duration) *WaitForTurnstileAction {
	a.WaitTimeout = timeout
	return a
}

// WithRequired 设置是否必需
func (a *WaitForTurnstileAction) WithRequired(required bool) *WaitForTurnstileAction {
	a.Required = required
	return a
}

// Execute 执行等待Turnstile出现操作
func (a *WaitForTurnstileAction) Execute(ctx *ActionContext) error {
	logger.Debug("等待Turnstile出现")

	selectors := ElementSelectors{
		`iframe[src*="turnstile"]`,
		`iframe[src*="cloudflare"]`,
		`.cf-turnstile`,
		`[data-sitekey]`,
		`div[id*="turnstile"]`,
		`div[class*="turnstile"]`,
	}

	_, err := selectors.FindElement(ctx, a.WaitTimeout)
	if err != nil {
		if a.Required {
			return fmt.Errorf("等待Turnstile出现失败: %w", err)
		} else {
			logger.Debug("未检测到Turnstile")
		}
	} else {
		logger.Debug("检测到Turnstile")
	}

	return nil
}

// GetName 获取操作名称
func (a *WaitForTurnstileAction) GetName() string {
	return "WaitForTurnstile"
}
