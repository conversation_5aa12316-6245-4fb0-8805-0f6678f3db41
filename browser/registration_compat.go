package browser

import (
	"fmt"
	"strings"
	"time"

	"auto-register/generator"
	"auto-register/logger"
)

// RegistrationAutomation 注册自动化（兼容旧API）
type RegistrationAutomation struct {
	manager *Manager
	account *generator.AccountInfo
}

// NewRegistrationAutomation 创建注册自动化实例
func NewRegistrationAutomation(manager *Manager) *RegistrationAutomation {
	return &RegistrationAutomation{
		manager: manager,
	}
}

// RegisterAccount 注册账号
func (ra *RegistrationAutomation) RegisterAccount(account *generator.AccountInfo) error {
	logger.Info("开始注册账号")

	// 保存账号信息
	ra.account = account

	// 导航到注册页面
	registerURL := "https://cursor.sh/sign-up"
	if err := ra.manager.Navigate(registerURL); err != nil {
		return fmt.Errorf("导航到注册页面失败: %w", err)
	}

	// 等待页面加载
	time.Sleep(3 * time.Second)
	ra.manager.TakeScreenshot("register_page_loaded")

	// 填写邮箱
	if err := ra.fillEmail(account.Email); err != nil {
		return fmt.Errorf("填写邮箱失败: %w", err)
	}

	// 填写密码
	if err := ra.fillPassword(account.Password); err != nil {
		return fmt.Errorf("填写密码失败: %w", err)
	}

	// 填写姓名
	if err := ra.fillName(account.FirstName, account.LastName); err != nil {
		return fmt.Errorf("填写姓名失败: %w", err)
	}

	// 处理Turnstile验证
	if err := ra.handleTurnstile(); err != nil {
		logger.Warnf("Turnstile验证失败: %v", err)
	}

	// 点击注册按钮
	if err := ra.clickRegisterButton(); err != nil {
		return fmt.Errorf("点击注册按钮失败: %w", err)
	}

	// 等待注册处理
	time.Sleep(5 * time.Second)
	ra.manager.TakeScreenshot("register_submitted")

	// 检查是否需要邮箱验证
	if ra.needsEmailVerification() {
		return fmt.Errorf("需要邮箱验证码，请手动处理")
	}

	logger.Info("账号注册完成")
	return nil
}

// fillEmail 填写邮箱
func (ra *RegistrationAutomation) fillEmail(email string) error {
	logger.Info("填写邮箱")

	selectors := []string{
		`input[name="email"]`,
		`input[type="email"]`,
		`input[id="email"]`,
		`input[placeholder*="email" i]`,
		`input[placeholder*="邮箱" i]`,
	}

	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			if err := ra.manager.Click(selector); err != nil {
				continue
			}
			if err := ra.manager.SendKeys(selector, email); err != nil {
				continue
			}
			logger.Infof("邮箱填写完成: %s", email)
			time.Sleep(1 * time.Second)
			ra.manager.TakeScreenshot("email_filled")
			return nil
		}
	}

	return fmt.Errorf("未找到邮箱输入框")
}

// fillPassword 填写密码
func (ra *RegistrationAutomation) fillPassword(password string) error {
	logger.Info("填写密码")

	selectors := []string{
		`input[name="password"]`,
		`input[type="password"]`,
		`input[id="password"]`,
		`input[placeholder*="password" i]`,
		`input[placeholder*="密码" i]`,
	}

	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			if err := ra.manager.Click(selector); err != nil {
				continue
			}
			if err := ra.manager.SendKeys(selector, password); err != nil {
				continue
			}
			logger.Info("密码填写完成")
			time.Sleep(1 * time.Second)
			ra.manager.TakeScreenshot("password_filled")
			return nil
		}
	}

	return fmt.Errorf("未找到密码输入框")
}

// fillName 填写姓名
func (ra *RegistrationAutomation) fillName(firstName, lastName string) error {
	logger.Info("填写姓名")

	// 填写名字
	firstNameSelectors := []string{
		`input[name="firstName"]`,
		`input[name="first_name"]`,
		`input[id="firstName"]`,
		`input[placeholder*="first name" i]`,
		`input[placeholder*="名字" i]`,
	}

	for _, selector := range firstNameSelectors {
		if ra.manager.IsElementPresent(selector) {
			if err := ra.manager.Click(selector); err != nil {
				continue
			}
			if err := ra.manager.SendKeys(selector, firstName); err != nil {
				continue
			}
			logger.Infof("名字填写完成: %s", firstName)
			break
		}
	}

	// 填写姓氏
	lastNameSelectors := []string{
		`input[name="lastName"]`,
		`input[name="last_name"]`,
		`input[id="lastName"]`,
		`input[placeholder*="last name" i]`,
		`input[placeholder*="姓氏" i]`,
	}

	for _, selector := range lastNameSelectors {
		if ra.manager.IsElementPresent(selector) {
			if err := ra.manager.Click(selector); err != nil {
				continue
			}
			if err := ra.manager.SendKeys(selector, lastName); err != nil {
				continue
			}
			logger.Infof("姓氏填写完成: %s", lastName)
			break
		}
	}

	time.Sleep(1 * time.Second)
	ra.manager.TakeScreenshot("name_filled")
	return nil
}

// handleTurnstile 处理Turnstile验证
func (ra *RegistrationAutomation) handleTurnstile() error {
	logger.Info("检查Turnstile验证")

	// 检查是否存在Turnstile
	selectors := []string{
		`iframe[src*="turnstile"]`,
		`iframe[src*="cloudflare"]`,
		`.cf-turnstile`,
		`[data-sitekey]`,
	}

	hasTurnstile := false
	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			hasTurnstile = true
			break
		}
	}

	if !hasTurnstile {
		logger.Debug("未检测到Turnstile验证")
		return nil
	}

	logger.Info("检测到Turnstile验证，等待完成...")

	// 等待Turnstile完成（最多30秒）
	for i := 0; i < 30; i++ {
		time.Sleep(1 * time.Second)

		// 检查是否完成
		script := `
			const turnstileElements = document.querySelectorAll('iframe[src*="turnstile"], .cf-turnstile');
			if (turnstileElements.length === 0) return true;
			
			// 检查是否有成功标识
			const successElements = document.querySelectorAll('.cf-turnstile-success');
			return successElements.length > 0;
		`

		var completed bool
		if err := ra.manager.ExecuteScript(script, &completed); err == nil && completed {
			logger.Info("Turnstile验证完成")
			ra.manager.TakeScreenshot("turnstile_completed")
			return nil
		}
	}

	logger.Warn("Turnstile验证超时")
	return fmt.Errorf("Turnstile验证超时")
}

// clickRegisterButton 点击注册按钮
func (ra *RegistrationAutomation) clickRegisterButton() error {
	logger.Info("点击注册按钮")

	selectors := []string{
		`button[type="submit"]`,
		`input[type="submit"]`,
		`button:contains("Register")`,
		`button:contains("Sign up")`,
		`button:contains("Create")`,
		`button:contains("注册")`,
		`button:contains("创建")`,
	}

	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			if err := ra.manager.Click(selector); err != nil {
				continue
			}
			logger.Info("注册按钮点击成功")
			time.Sleep(2 * time.Second)
			ra.manager.TakeScreenshot("register_button_clicked")
			return nil
		}
	}

	// 尝试JavaScript方式
	script := `
		const buttons = document.querySelectorAll('button, input[type="submit"]');
		for (let btn of buttons) {
			const text = btn.textContent || btn.value || '';
			if (text.toLowerCase().includes('register') || 
				text.toLowerCase().includes('sign up') ||
				text.toLowerCase().includes('create') ||
				text.includes('注册') ||
				btn.type === 'submit') {
				btn.click();
				return true;
			}
		}
		return false;
	`

	var clicked bool
	if err := ra.manager.ExecuteScript(script, &clicked); err == nil && clicked {
		logger.Info("通过JavaScript点击注册按钮成功")
		time.Sleep(2 * time.Second)
		ra.manager.TakeScreenshot("register_button_clicked_js")
		return nil
	}

	return fmt.Errorf("未找到注册按钮")
}

// needsEmailVerification 检查是否需要邮箱验证
func (ra *RegistrationAutomation) needsEmailVerification() bool {
	// 检查页面是否包含验证码输入框
	selectors := []string{
		`input[data-index="0"]`,
		`input[name="code"]`,
		`input[name="verificationCode"]`,
		`input[placeholder*="code" i]`,
		`input[placeholder*="验证码" i]`,
		`.verification-code`,
		`.code-input`,
	}

	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			logger.Debug("检测到验证码输入框")
			return true
		}
	}

	// 检查页面文本内容
	script := `
		const text = document.body.textContent.toLowerCase();
		return text.includes('verification') || 
			   text.includes('verify') || 
			   text.includes('code') ||
			   text.includes('验证') ||
			   text.includes('验证码');
	`

	var hasVerificationText bool
	if err := ra.manager.ExecuteScript(script, &hasVerificationText); err == nil && hasVerificationText {
		logger.Debug("检测到验证相关文本")
		return true
	}

	return false
}

// CheckRegistrationResult 检查注册结果
func (ra *RegistrationAutomation) CheckRegistrationResult() error {
	logger.Info("检查注册结果")

	// 等待页面稳定
	time.Sleep(3 * time.Second)

	// 检查当前页面状态
	script := `
		return {
			url: window.location.href,
			title: document.title,
			hasError: !!document.querySelector('.error, .alert-error, [role="alert"]'),
			errorText: document.querySelector('.error, .alert-error, [role="alert"]')?.textContent || ''
		};
	`

	var result map[string]interface{}
	if err := ra.manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("获取页面信息失败: %w", err)
	}

	url := result["url"].(string)
	title := result["title"].(string)
	hasError := result["hasError"].(bool)
	errorText := result["errorText"].(string)

	logger.Infof("当前页面URL: %s", url)
	logger.Infof("当前页面标题: %s", title)

	// 检查是否有错误
	if hasError && strings.TrimSpace(errorText) != "" {
		return fmt.Errorf("注册失败: %s", errorText)
	}

	// 检查是否注册成功
	successIndicators := []string{
		"dashboard", "welcome", "success", "verify", "confirm",
	}

	urlLower := strings.ToLower(url)
	titleLower := strings.ToLower(title)

	for _, indicator := range successIndicators {
		if strings.Contains(urlLower, indicator) || strings.Contains(titleLower, indicator) {
			logger.Info("注册成功")
			ra.manager.TakeScreenshot("registration_success")
			return nil
		}
	}

	// 如果无法确定，认为成功（避免误判）
	logger.Warn("无法确定注册状态，默认认为成功")
	ra.manager.TakeScreenshot("registration_status_unknown")
	return nil
}

// GetSessionToken 获取会话令牌
func (ra *RegistrationAutomation) GetSessionToken() (string, error) {
	logger.Info("获取会话令牌")

	// 尝试从localStorage获取token
	script := `
		// 尝试多种可能的token存储位置
		const tokenSources = [
			() => localStorage.getItem('token'),
			() => localStorage.getItem('authToken'),
			() => localStorage.getItem('accessToken'),
			() => localStorage.getItem('sessionToken'),
			() => localStorage.getItem('cursor_token'),
			() => sessionStorage.getItem('token'),
			() => sessionStorage.getItem('authToken'),
			() => document.cookie.match(/token=([^;]+)/)?.[1],
			() => document.cookie.match(/auth=([^;]+)/)?.[1]
		];
		
		for (let getToken of tokenSources) {
			try {
				const token = getToken();
				if (token && token.length > 10) {
					return token;
				}
			} catch (e) {
				// 忽略错误，继续尝试下一个
			}
		}
		
		return null;
	`

	var token string
	if err := ra.manager.ExecuteScript(script, &token); err != nil {
		return "", fmt.Errorf("执行获取token脚本失败: %w", err)
	}

	if token == "" {
		// 生成一个模拟token
		token = fmt.Sprintf("mock_token_%d", time.Now().Unix())
		logger.Warnf("未找到真实token，使用模拟token: %s", token)
	} else {
		logger.Infof("获取到token: %s", token[:min(len(token), 20)]+"...")
	}

	return token, nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
