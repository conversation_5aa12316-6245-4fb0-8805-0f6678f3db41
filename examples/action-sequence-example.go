package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"auto-register/browser"
	"auto-register/browser/automation/action"
	"auto-register/config"
	"auto-register/logger"
)

func main() {
	// 初始化日志
	if err := logger.InitLogger("INFO", ""); err != nil {
		log.Fatalf("日志初始化失败: %v", err)
	}

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 从环境变量获取登录凭据
	email := os.Getenv("AUGMENT_EMAIL")
	password := os.Getenv("AUGMENT_PASSWORD")

	if email == "" || password == "" {
		fmt.Println("使用方法:")
		fmt.Println("export AUGMENT_EMAIL=<EMAIL>")
		fmt.Println("export AUGMENT_PASSWORD=your-password")
		fmt.Println("go run examples/action-sequence-example.go")
		os.Exit(1)
	}

	// 初始化浏览器管理器
	browserManager := browser.NewManager(cfg)
	if err := browserManager.Start(); err != nil {
		log.Fatalf("启动浏览器失败: %v", err)
	}
	defer browserManager.Stop()

	// 创建操作上下文
	ctx := action.NewActionContext(browserManager)

	// 演示不同的登录方式
	demonstrateLoginMethods(ctx, email, password)

	fmt.Println("\n按回车键退出...")
	fmt.Scanln()
}

func demonstrateLoginMethods(ctx *action.ActionContext, email, password string) {
	fmt.Println("\n=== 操作序列演示 ===")

	// 方法1: 使用预定义的快速登录序列
	fmt.Println("\n1. 使用快速登录序列:")
	if err := demonstrateQuickLogin(ctx, email, password); err != nil {
		logger.Errorf("快速登录失败: %v", err)
	}

	// 方法2: 使用最小登录序列
	fmt.Println("\n2. 使用最小登录序列:")
	if err := demonstrateMinimalLogin(ctx, email, password); err != nil {
		logger.Errorf("最小登录失败: %v", err)
	}

	// 方法3: 使用自定义序列构建器
	fmt.Println("\n3. 使用自定义序列构建器:")
	if err := demonstrateCustomSequence(ctx, email, password); err != nil {
		logger.Errorf("自定义序列失败: %v", err)
	}

	// 方法4: 使用单个操作组合
	fmt.Println("\n4. 使用单个操作组合:")
	if err := demonstrateIndividualActions(ctx, email, password); err != nil {
		logger.Errorf("单个操作组合失败: %v", err)
	}

	// 方法5: 使用调试序列
	fmt.Println("\n5. 使用调试序列:")
	if err := demonstrateDebugLogin(ctx, email, password); err != nil {
		logger.Errorf("调试登录失败: %v", err)
	}
}

// 演示快速登录序列
func demonstrateQuickLogin(ctx *action.ActionContext, email, password string) error {
	logger.Info("演示快速登录序列")
	
	sequence := action.QuickLoginSequence(email, password)
	return sequence.Execute(ctx)
}

// 演示最小登录序列
func demonstrateMinimalLogin(ctx *action.ActionContext, email, password string) error {
	logger.Info("演示最小登录序列")
	
	sequence := action.MinimalLoginSequence(email, password)
	return sequence.Execute(ctx)
}

// 演示自定义序列构建器
func demonstrateCustomSequence(ctx *action.ActionContext, email, password string) error {
	logger.Info("演示自定义序列构建器")
	
	// 使用构建器创建自定义序列
	sequence := action.NewAugmentLoginSequence().
		WithLog("开始自定义登录流程").
		WithNavigate("https://login.augmentcode.com/").
		WithScreenshot("custom_login_page").
		WithDelay(1*time.Second, "等待页面稳定").
		WithInputEmail(email).
		WithScreenshot("custom_email_input").
		WithContinueButton().
		WithDelay(2*time.Second, "等待密码页面").
		WithInputPassword(password).
		WithLoginButton().
		WithVerifyResult().
		WithLog("自定义登录流程完成").
		GetSequence()
	
	return sequence.Execute(ctx)
}

// 演示单个操作组合
func demonstrateIndividualActions(ctx *action.ActionContext, email, password string) error {
	logger.Info("演示单个操作组合")
	
	// 手动创建操作序列
	sequence := action.NewActionSequence("IndividualActions")
	
	// 添加各个操作
	sequence.Add(action.NewLogAction("开始单个操作演示"))
	sequence.Add(action.NewNavigateAction("https://login.augmentcode.com/").
		WithWaitTime(2 * time.Second).
		WithScreenshot(true, "individual_page_loaded"))
	
	// 输入邮箱
	emailAction := action.NewInputEmailAction(email).
		WithTimeout(10 * time.Second).
		WithScreenshot(true, "individual_email_input")
	sequence.Add(emailAction)
	
	// 点击继续
	continueAction := action.NewContinueButtonAction().
		WithTimeout(5 * time.Second).
		WithScreenshot(true, "individual_continue_clicked")
	sequence.Add(continueAction)
	
	// 等待密码页面
	sequence.Add(action.NewDelayAction(3 * time.Second).
		WithDescription("等待密码页面加载"))
	
	// 输入密码
	passwordAction := action.NewInputPasswordAction(password).
		WithTimeout(10 * time.Second).
		WithScreenshot(true, "individual_password_input")
	sequence.Add(passwordAction)
	
	// 点击登录
	loginAction := action.NewLoginButtonAction().
		WithTimeout(5 * time.Second).
		WithScreenshot(true, "individual_login_clicked")
	sequence.Add(loginAction)
	
	// 验证结果
	verifyAction := action.NewVerifyLoginResultAction().
		WithWaitTime(5 * time.Second).
		WithAllowUnknownStatus(true)
	sequence.Add(verifyAction)
	
	sequence.Add(action.NewLogAction("单个操作演示完成"))
	
	return sequence.Execute(ctx)
}

// 演示调试登录序列
func demonstrateDebugLogin(ctx *action.ActionContext, email, password string) error {
	logger.Info("演示调试登录序列")
	
	sequence := action.DebugLoginSequence(email, password)
	return sequence.Execute(ctx)
}

// 演示错误处理
func demonstrateErrorHandling(ctx *action.ActionContext) {
	logger.Info("演示错误处理")
	
	// 创建一个会失败的序列
	sequence := action.NewActionSequence("ErrorDemo").
		WithStopOnError(false). // 不在错误时停止
		Add(action.NewLogAction("开始错误处理演示")).
		Add(action.NewNavigateAction("https://invalid-url-that-will-fail.com/")).
		Add(action.NewLogAction("这条日志应该会显示，因为设置了不在错误时停止")).
		Add(action.NewScreenshotAction("error_demo"))
	
	if err := sequence.Execute(ctx); err != nil {
		logger.Warnf("序列执行有错误（这是预期的）: %v", err)
	}
}

// 演示高级功能
func demonstrateAdvancedFeatures(ctx *action.ActionContext) {
	logger.Info("演示高级功能")
	
	// 创建包含高级功能的序列
	sequence := action.NewActionSequence("AdvancedDemo").
		Add(action.NewLogAction("开始高级功能演示")).
		Add(action.NewExecuteScriptAction("return document.title;").
			WithDescription("获取页面标题").
			WithStoreResult("page_title")).
		Add(action.NewRandomDelayAction(1*time.Second, 3*time.Second)).
		Add(action.NewSavePageHTMLAction("advanced_demo")).
		Add(action.NewLogAction("高级功能演示完成"))
	
	if err := sequence.Execute(ctx); err != nil {
		logger.Errorf("高级功能演示失败: %v", err)
	}
}
