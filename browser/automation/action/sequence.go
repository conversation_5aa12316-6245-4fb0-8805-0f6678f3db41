package action

import (
	"fmt"
	"time"

	"auto-register/logger"
)

// ActionSequence 操作序列
type ActionSequence struct {
	Name        string
	Actions     []Action
	StopOnError bool
	Timeout     time.Duration
}

// NewActionSequence 创建操作序列
func NewActionSequence(name string) *ActionSequence {
	return &ActionSequence{
		Name:        name,
		Actions:     make([]Action, 0),
		StopOnError: true,
		Timeout:     30 * time.Minute, // 默认30分钟超时
	}
}

// WithStopOnError 设置遇到错误时是否停止
func (s *ActionSequence) WithStopOnError(stop bool) *ActionSequence {
	s.StopOnError = stop
	return s
}

// WithTimeout 设置总超时时间
func (s *ActionSequence) WithTimeout(timeout time.Duration) *ActionSequence {
	s.Timeout = timeout
	return s
}

// Add 添加操作到序列
func (s *ActionSequence) Add(action Action) *ActionSequence {
	s.Actions = append(s.Actions, action)
	return s
}

// AddMultiple 添加多个操作到序列
func (s *ActionSequence) AddMultiple(actions ...Action) *ActionSequence {
	s.Actions = append(s.Actions, actions...)
	return s
}

// Execute 执行操作序列
func (s *ActionSequence) Execute(ctx *ActionContext) error {
	logger.Infof("开始执行操作序列: %s", s.Name)
	start := time.Now()

	for i, action := range s.Actions {
		// 检查超时
		if time.Since(start) > s.Timeout {
			return fmt.Errorf("操作序列执行超时: %v", s.Timeout)
		}

		logger.Infof("执行操作 %d/%d: %s", i+1, len(s.Actions), action.GetName())

		if err := action.Execute(ctx); err != nil {
			logger.Errorf("操作 %s 执行失败: %v", action.GetName(), err)

			if s.StopOnError {
				return fmt.Errorf("操作序列在第 %d 步失败: %w", i+1, err)
			} else {
				logger.Warnf("忽略错误，继续执行下一个操作")
			}
		} else {
			logger.Debugf("操作 %s 执行成功", action.GetName())
		}
	}

	duration := time.Since(start)
	logger.Infof("操作序列 %s 执行完成，耗时: %v", s.Name, duration)
	return nil
}

// GetName 获取操作名称
func (s *ActionSequence) GetName() string {
	return fmt.Sprintf("Sequence[%s]", s.Name)
}

// AugmentLoginSequence Augment登录操作序列构建器
type AugmentLoginSequence struct {
	sequence *ActionSequence
}

// NewAugmentLoginSequence 创建Augment登录序列
func NewAugmentLoginSequence() *AugmentLoginSequence {
	return &AugmentLoginSequence{
		sequence: NewActionSequence("AugmentLogin"),
	}
}

// Build 构建完整的登录操作序列
func (b *AugmentLoginSequence) Build(credentials *LoginCredentials) *ActionSequence {
	return b.sequence.
		Add(NewLogAction("开始Augment账号登录")).
		Add(NewNavigateAction("https://login.augmentcode.com/").
			WithScreenshot(true, "login_page_loaded")).
		Add(NewInputWithSelectorsAction(CommonInputSelectors, credentials.Email).
			WithScreenshot(true, "email_input_completed")).
		Add(NewContinueButtonAction()).
		Add(NewDelayAction(2 * time.Second).
			WithDescription("等待密码页面加载")).
		Add(NewScreenshotAction("password_page_loaded")).
		Add(NewInputWithSelectorsAction(PasswordInputSelectors, credentials.Password)).
		Add(NewLoginButtonAction()).
		Add(NewVerifyLoginResultAction()).
		Add(NewLogAction("Augment账号登录完成"))
}

// BuildCustom 构建自定义登录序列
func (b *AugmentLoginSequence) BuildCustom() *ActionSequence {
	return b.sequence
}

// WithNavigate 添加导航步骤
func (b *AugmentLoginSequence) WithNavigate(url string) *AugmentLoginSequence {
	b.sequence.Add(NewNavigateAction(url).WithScreenshot(true, "page_loaded"))
	return b
}

// WithInputEmail 添加输入邮箱步骤
func (b *AugmentLoginSequence) WithInputEmail(email string) *AugmentLoginSequence {
	b.sequence.Add(NewInputWithSelectorsAction(CommonInputSelectors, email))
	return b
}

// WithContinueButton 添加点击继续按钮步骤
func (b *AugmentLoginSequence) WithContinueButton() *AugmentLoginSequence {
	b.sequence.Add(NewContinueButtonAction())
	return b
}

// WithInputPassword 添加输入密码步骤
func (b *AugmentLoginSequence) WithInputPassword(password string) *AugmentLoginSequence {
	b.sequence.Add(NewInputWithSelectorsAction(PasswordInputSelectors, password))
	return b
}

// WithLoginButton 添加点击登录按钮步骤
func (b *AugmentLoginSequence) WithLoginButton() *AugmentLoginSequence {
	b.sequence.Add(NewLoginButtonAction())
	return b
}

// WithVerifyResult 添加验证结果步骤
func (b *AugmentLoginSequence) WithVerifyResult() *AugmentLoginSequence {
	b.sequence.Add(NewVerifyLoginResultAction())
	return b
}

// WithDelay 添加延迟步骤
func (b *AugmentLoginSequence) WithDelay(duration time.Duration, description string) *AugmentLoginSequence {
	b.sequence.Add(NewDelayAction(duration).WithDescription(description))
	return b
}

// WithScreenshot 添加截图步骤
func (b *AugmentLoginSequence) WithScreenshot(filename string) *AugmentLoginSequence {
	b.sequence.Add(NewScreenshotAction(filename))
	return b
}

// WithLog 添加日志步骤
func (b *AugmentLoginSequence) WithLog(message string) *AugmentLoginSequence {
	b.sequence.Add(NewLogAction(message))
	return b
}

// WithCustomAction 添加自定义操作
func (b *AugmentLoginSequence) WithCustomAction(action Action) *AugmentLoginSequence {
	b.sequence.Add(action)
	return b
}

// GetSequence 获取构建的序列
func (b *AugmentLoginSequence) GetSequence() *ActionSequence {
	return b.sequence
}

// QuickLoginSequence 快速登录序列（预定义的常用组合）
func QuickLoginSequence(email, password string) *ActionSequence {
	return NewAugmentLoginSequence().Build(&LoginCredentials{
		Email:    email,
		Password: password,
	})
}

// MinimalLoginSequence 最小登录序列（只包含核心步骤）
func MinimalLoginSequence(email, password string) *ActionSequence {
	return NewActionSequence("MinimalLogin").
		Add(NewNavigateAction("https://login.augmentcode.com/")).
		Add(NewInputWithSelectorsAction(CommonInputSelectors, email)).
		Add(NewContinueButtonAction()).
		Add(NewInputWithSelectorsAction(PasswordInputSelectors, password)).
		Add(NewLoginButtonAction()).
		Add(NewVerifyLoginResultAction())
}

// DebugLoginSequence 调试登录序列（包含详细的调试信息）
func DebugLoginSequence(email, password string) *ActionSequence {
	return NewActionSequence("DebugLogin").
		Add(NewLogAction("=== 开始调试登录流程 ===")).
		Add(NewNavigateAction("https://login.augmentcode.com/").
			WithScreenshot(true, "01_login_page_loaded")).
		Add(NewSavePageHTMLAction("01_login_page")).
		Add(NewGetPageInfoAction()).
		Add(NewInputWithSelectorsAction(CommonInputSelectors, email).
			WithScreenshot(true, "02_email_input")).
		Add(NewContinueButtonAction().
			WithScreenshot(true, "03_continue_clicked")).
		Add(NewDelayAction(3 * time.Second).
			WithDescription("等待密码页面完全加载")).
		Add(NewSavePageHTMLAction("04_password_page")).
		Add(NewInputWithSelectorsAction(PasswordInputSelectors, password).
			WithScreenshot(true, "05_password_input")).
		Add(NewLoginButtonAction().
			WithScreenshot(true, "06_login_clicked")).
		Add(NewDelayAction(5 * time.Second).
			WithDescription("等待登录处理")).
		Add(NewSavePageHTMLAction("07_after_login")).
		Add(NewVerifyLoginResultAction()).
		Add(NewGetPageInfoAction()).
		Add(NewLogAction("=== 调试登录流程完成 ==="))
}
