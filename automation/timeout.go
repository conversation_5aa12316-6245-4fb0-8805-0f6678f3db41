package automation

import (
	"time"

	"auto-register/browser/automation/action"
	"auto-register/browser/automation/step"
	"auto-register/config"
	"auto-register/logger"
)

// TimeoutManager 超时管理器
type TimeoutManager struct {
	config *config.Config
}

// NewTimeoutManager 创建超时管理器
func NewTimeoutManager(cfg *config.Config) *TimeoutManager {
	return &TimeoutManager{
		config: cfg,
	}
}

// GetElementWaitTimeout 获取元素等待超时时间
func (tm *TimeoutManager) GetElementWaitTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.ElementWaitTimeout) * time.Second
}

// GetPageLoadTimeout 获取页面加载超时时间
func (tm *TimeoutManager) GetPageLoadTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.PageLoadTimeout) * time.Second
}

// GetTurnstileTimeout 获取Turnstile验证超时时间
func (tm *TimeoutManager) GetTurnstileTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.TurnstileTimeout) * time.Second
}

// GetInputTimeout 获取输入操作超时时间
func (tm *TimeoutManager) GetInputTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.InputTimeout) * time.Second
}

// GetButtonTimeout 获取按钮操作超时时间
func (tm *TimeoutManager) GetButtonTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.ButtonTimeout) * time.Second
}

// GetNavigationTimeout 获取导航超时时间
func (tm *TimeoutManager) GetNavigationTimeout() time.Duration {
	return time.Duration(tm.config.FlowConfig.NavigationTimeout) * time.Second
}

// ApplyToInputAction 应用超时配置到输入操作
func (tm *TimeoutManager) ApplyToInputAction(inputAction *action.InputElementAction) *action.InputElementAction {
	return inputAction.WithTimeout(tm.GetInputTimeout())
}

// ApplyToInputWithSelectorsAction 应用超时配置到多选择器输入操作
func (tm *TimeoutManager) ApplyToInputWithSelectorsAction(inputAction *action.InputWithSelectorsAction) *action.InputWithSelectorsAction {
	return inputAction.WithTimeout(tm.GetInputTimeout())
}

// ApplyToButtonAction 应用超时配置到按钮操作
func (tm *TimeoutManager) ApplyToButtonAction(buttonAction *action.ButtonElementAction) *action.ButtonElementAction {
	return buttonAction.WithTimeout(tm.GetButtonTimeout())
}

// ApplyToButtonClickAction 应用超时配置到按钮点击操作
func (tm *TimeoutManager) ApplyToButtonClickAction(buttonAction *action.ButtonClickAction) *action.ButtonClickAction {
	return buttonAction.WithTimeout(tm.GetButtonTimeout())
}

// ApplyToTurnstileAction 应用超时配置到Turnstile操作
func (tm *TimeoutManager) ApplyToTurnstileAction(turnstileAction *action.TurnstileAction) *action.TurnstileAction {
	return turnstileAction.WithTimeout(tm.GetTurnstileTimeout())
}

// ApplyToNavigateAction 应用超时配置到导航操作
func (tm *TimeoutManager) ApplyToNavigateAction(navigateAction *action.NavigateAction) *action.NavigateAction {
	return navigateAction.WithTimeout(tm.GetNavigationTimeout())
}

// ApplyToWaitForElementAction 应用超时配置到等待元素操作
func (tm *TimeoutManager) ApplyToWaitForElementAction(waitAction *action.WaitForElementAction) *action.WaitForElementAction {
	return waitAction.WithTimeout(tm.GetElementWaitTimeout())
}

// ApplyToWaitForPageLoadStep 应用超时配置到等待页面加载步骤
func (tm *TimeoutManager) ApplyToWaitForPageLoadStep(waitStep *step.WaitForPageLoadStep) *step.WaitForPageLoadStep {
	return waitStep.WithTimeout(tm.GetPageLoadTimeout())
}

// ApplyToHandleTurnstileStep 应用超时配置到处理Turnstile步骤
func (tm *TimeoutManager) ApplyToHandleTurnstileStep(turnstileStep *step.HandleTurnstileStep) *step.HandleTurnstileStep {
	return turnstileStep.WithTimeout(tm.GetTurnstileTimeout())
}

// ApplyToWaitForElementStep 应用超时配置到等待元素步骤
func (tm *TimeoutManager) ApplyToWaitForElementStep(waitStep *step.WaitForElementStep) *step.WaitForElementStep {
	return waitStep.WithTimeout(tm.GetElementWaitTimeout())
}

// CreateInputActionWithTimeout 创建带超时配置的输入操作
func (tm *TimeoutManager) CreateInputActionWithTimeout(selector, value string) *action.InputElementAction {
	return action.NewInputElementAction(selector, value).
		WithTimeout(tm.GetInputTimeout())
}

// CreateInputWithSelectorsActionWithTimeout 创建带超时配置的多选择器输入操作
func (tm *TimeoutManager) CreateInputWithSelectorsActionWithTimeout(selectors action.ElementSelectors, value string) *action.InputWithSelectorsAction {
	return action.NewInputWithSelectorsAction(selectors, value).
		WithTimeout(tm.GetInputTimeout())
}

// CreateButtonActionWithTimeout 创建带超时配置的按钮操作
func (tm *TimeoutManager) CreateButtonActionWithTimeout(selector string) *action.ButtonElementAction {
	return action.NewButtonElementAction(selector).
		WithTimeout(tm.GetButtonTimeout())
}

// CreateButtonClickActionWithTimeout 创建带超时配置的按钮点击操作
func (tm *TimeoutManager) CreateButtonClickActionWithTimeout(selectors action.ElementSelectors) *action.ButtonClickAction {
	return action.NewButtonClickAction(selectors).
		WithTimeout(tm.GetButtonTimeout())
}

// CreateTurnstileActionWithTimeout 创建带超时配置的Turnstile操作
func (tm *TimeoutManager) CreateTurnstileActionWithTimeout() *action.TurnstileAction {
	return action.NewTurnstileAction().
		WithTimeout(tm.GetTurnstileTimeout())
}

// CreateNavigateActionWithTimeout 创建带超时配置的导航操作
func (tm *TimeoutManager) CreateNavigateActionWithTimeout(url string) *action.NavigateAction {
	return action.NewNavigateAction(url).
		WithTimeout(tm.GetNavigationTimeout())
}

// CreateWaitForElementActionWithTimeout 创建带超时配置的等待元素操作
func (tm *TimeoutManager) CreateWaitForElementActionWithTimeout(selectors action.ElementSelectors) *action.WaitForElementAction {
	return action.NewWaitForElementAction(selectors, tm.GetElementWaitTimeout())
}

// CreateWaitForPageLoadStepWithTimeout 创建带超时配置的等待页面加载步骤
func (tm *TimeoutManager) CreateWaitForPageLoadStepWithTimeout() *step.WaitForPageLoadStep {
	return step.NewWaitForPageLoadStep().
		WithTimeout(tm.GetPageLoadTimeout())
}

// CreateHandleTurnstileStepWithTimeout 创建带超时配置的处理Turnstile步骤
func (tm *TimeoutManager) CreateHandleTurnstileStepWithTimeout() *step.HandleTurnstileStep {
	return step.NewHandleTurnstileStep().
		WithTimeout(tm.GetTurnstileTimeout())
}

// CreateWaitForElementStepWithTimeout 创建带超时配置的等待元素步骤
func (tm *TimeoutManager) CreateWaitForElementStepWithTimeout(selectors action.ElementSelectors, description string) *step.WaitForElementStep {
	return step.NewWaitForElementStep(selectors, description).
		WithTimeout(tm.GetElementWaitTimeout())
}

// LogTimeoutConfig 记录超时配置信息
func (tm *TimeoutManager) LogTimeoutConfig() {
	logger.Info("超时配置:")
	logger.Infof("  元素等待超时: %v", tm.GetElementWaitTimeout())
	logger.Infof("  页面加载超时: %v", tm.GetPageLoadTimeout())
	logger.Infof("  Turnstile超时: %v", tm.GetTurnstileTimeout())
	logger.Infof("  输入操作超时: %v", tm.GetInputTimeout())
	logger.Infof("  按钮操作超时: %v", tm.GetButtonTimeout())
	logger.Infof("  导航超时: %v", tm.GetNavigationTimeout())
}

// ValidateTimeouts 验证超时配置的合理性
func (tm *TimeoutManager) ValidateTimeouts() error {
	timeouts := map[string]int{
		"元素等待超时": tm.config.FlowConfig.ElementWaitTimeout,
		"页面加载超时": tm.config.FlowConfig.PageLoadTimeout,
		"Turnstile超时": tm.config.FlowConfig.TurnstileTimeout,
		"输入操作超时": tm.config.FlowConfig.InputTimeout,
		"按钮操作超时": tm.config.FlowConfig.ButtonTimeout,
		"导航超时":    tm.config.FlowConfig.NavigationTimeout,
	}

	for name, timeout := range timeouts {
		if timeout <= 0 {
			logger.Warnf("%s配置为%d秒，可能过短", name, timeout)
		}
		if timeout > 300 {
			logger.Warnf("%s配置为%d秒，可能过长", name, timeout)
		}
	}

	return nil
}

// GetRecommendedTimeouts 获取推荐的超时配置
func (tm *TimeoutManager) GetRecommendedTimeouts() map[string]int {
	return map[string]int{
		"ElementWaitTimeout": 10,  // 元素等待超时
		"PageLoadTimeout":    30,  // 页面加载超时
		"TurnstileTimeout":   30,  // Turnstile验证超时
		"InputTimeout":       5,   // 输入操作超时
		"ButtonTimeout":      5,   // 按钮操作超时
		"NavigationTimeout":  30,  // 导航超时
	}
}
