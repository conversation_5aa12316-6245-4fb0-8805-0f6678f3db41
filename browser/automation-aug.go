package browser

import (
	"fmt"
	"os"
	"strings"
	"time"

	"auto-register/generator"
	"auto-register/logger"
)

// AugmentLoginAutomation Augment登录自动化
type AugmentLoginAutomation struct {
	manager *Manager
}

// NewAugmentLoginAutomation 创建Augment登录自动化实例
func NewAugmentLoginAutomation(manager *Manager) *AugmentLoginAutomation {
	return &AugmentLoginAutomation{
		manager: manager,
	}
}

// LoginAccount 自动登录账号
func (ala *AugmentLoginAutomation) LoginAccount(email, password string) error {
	logger.Info("开始Augment账号登录")

	// 导航到登录页面
	loginURL := "https://login.augmentcode.com/"
	if err := ala.manager.Navigate(loginURL); err != nil {
		return fmt.Errorf("导航到登录页面失败: %w", err)
	}

	// 等待页面加载
	time.Sleep(2 * time.Second)
	ala.manager.TakeScreenshot("login_page_loaded")

	// 第一步：输入邮箱/用户名
	if err := ala.inputIdentifier(email); err != nil {
		return fmt.Errorf("输入邮箱失败: %w", err)
	}

	// 第二步：输入密码
	if err := ala.inputPassword(password); err != nil {
		return fmt.Errorf("输入密码失败: %w", err)
	}

	// 检查登录结果
	if err := ala.checkLoginResult(); err != nil {
		return fmt.Errorf("登录验证失败: %w", err)
	}

	logger.Info("账号登录完成")
	return nil
}

// inputIdentifier 输入邮箱/用户名标识符
func (ala *AugmentLoginAutomation) inputIdentifier(email string) error {
	logger.Info("输入邮箱标识符")

	// 等待标识符输入框出现 - Auth0通用选择器
	selectors := []string{
		`input[name="username"]`,
		`input[name="email"]`,
		`input[type="email"]`,
		`input[id="username"]`,
		`input[id="email"]`,
		`input[data-testid="username"]`,
		`input[data-testid="email"]`,
		`input[placeholder*="email" i]`,
		`input[placeholder*="username" i]`,
		`input[placeholder*="邮箱" i]`,
		`input[placeholder*="用户名" i]`,
	}

	var inputSelector string
	var err error

	// 尝试找到可用的输入框
	for _, selector := range selectors {
		if err = ala.manager.WaitForElement(selector, 3*time.Second); err == nil {
			inputSelector = selector
			logger.Debugf("找到标识符输入框: %s", selector)
			break
		}
	}

	if inputSelector == "" {
		ala.manager.TakeScreenshot("identifier_input_not_found")
		return fmt.Errorf("未找到邮箱输入框")
	}

	// 清空输入框并输入邮箱
	if err := ala.manager.Click(inputSelector); err != nil {
		return fmt.Errorf("点击邮箱输入框失败: %w", err)
	}
	HumanLikeDelay()

	// 清空现有内容
	if err := ala.clearInput(inputSelector); err != nil {
		logger.Warnf("清空输入框失败: %v", err)
	}

	// 输入邮箱
	if err := ala.manager.SendKeys(inputSelector, email); err != nil {
		return fmt.Errorf("输入邮箱失败: %w", err)
	}
	logger.Infof("输入邮箱: %s", email)
	HumanLikeDelay()

	// 查找并点击继续按钮
	if err := ala.clickContinueButton(); err != nil {
		return fmt.Errorf("点击继续按钮失败: %w", err)
	}

	// 等待密码页面加载
	time.Sleep(2 * time.Second)
	ala.manager.TakeScreenshot("password_page_loaded")

	return nil
}

// inputPassword 输入密码
func (ala *AugmentLoginAutomation) inputPassword(password string) error {
	logger.Info("输入密码")

	// 等待密码输入框出现
	selectors := []string{
		`input[name="password"]`,
		`input[type="password"]`,
		`input[id="password"]`,
		`input[data-testid="password"]`,
		`input[placeholder*="password" i]`,
		`input[placeholder*="密码" i]`,
	}

	var inputSelector string
	var err error

	// 尝试找到密码输入框
	for _, selector := range selectors {
		if err = ala.manager.WaitForElement(selector, 5*time.Second); err == nil {
			inputSelector = selector
			logger.Debugf("找到密码输入框: %s", selector)
			break
		}
	}

	if inputSelector == "" {
		ala.manager.TakeScreenshot("password_input_not_found")
		return fmt.Errorf("未找到密码输入框")
	}

	// 点击密码输入框
	if err := ala.manager.Click(inputSelector); err != nil {
		return fmt.Errorf("点击密码输入框失败: %w", err)
	}
	HumanLikeDelay()

	// 清空现有内容
	if err := ala.clearInput(inputSelector); err != nil {
		logger.Warnf("清空密码输入框失败: %v", err)
	}

	// 输入密码
	if err := ala.manager.SendKeys(inputSelector, password); err != nil {
		return fmt.Errorf("输入密码失败: %w", err)
	}
	logger.Info("密码输入完成")
	HumanLikeDelay()

	// 查找并点击登录按钮
	if err := ala.clickLoginButton(); err != nil {
		return fmt.Errorf("点击登录按钮失败: %w", err)
	}

	// 等待登录处理
	time.Sleep(3 * time.Second)
	ala.manager.TakeScreenshot("login_submitted")

	return nil
}

// clickContinueButton 点击继续按钮
func (ala *AugmentLoginAutomation) clickContinueButton() error {
	logger.Debug("查找继续按钮")

	// Auth0常见的继续按钮选择器
	selectors := []string{
		`button[type="submit"]`,
		`button[data-action-button-primary="true"]`,
		`button[name="action"]`,
		`input[type="submit"]`,
		`button:contains("Continue")`,
		`button:contains("继续")`,
		`button:contains("Next")`,
		`button:contains("下一步")`,
		`[data-testid="continue-button"]`,
		`[data-testid="submit-button"]`,
		`.auth0-lock-submit`,
		`.auth0-lock-submit-button`,
	}

	for _, selector := range selectors {
		if ala.manager.IsElementPresent(selector) {
			logger.Debugf("找到继续按钮: %s", selector)
			if err := ala.manager.Click(selector); err != nil {
				logger.Debugf("点击按钮失败: %s, 错误: %v", selector, err)
				continue
			}
			logger.Info("继续按钮点击成功")
			return nil
		}
	}

	// 尝试使用JavaScript查找包含特定文本的按钮
	script := `
		const buttons = document.querySelectorAll('button, input[type="submit"]');
		for (let btn of buttons) {
			const text = btn.textContent || btn.value || '';
			if (text.toLowerCase().includes('continue') ||
				text.toLowerCase().includes('next') ||
				text.includes('继续') ||
				text.includes('下一步') ||
				btn.type === 'submit') {
				btn.click();
				return { success: true, text: text };
			}
		}
		return { success: false };
	`

	var result map[string]interface{}
	if err := ala.manager.ExecuteScript(script, &result); err == nil {
		if success, ok := result["success"].(bool); ok && success {
			logger.Info("通过JavaScript点击继续按钮成功")
			return nil
		}
	}

	ala.manager.TakeScreenshot("continue_button_not_found")
	return fmt.Errorf("未找到继续按钮")
}

// clickLoginButton 点击登录按钮
func (ala *AugmentLoginAutomation) clickLoginButton() error {
	logger.Debug("查找登录按钮")

	// 常见的登录按钮选择器
	selectors := []string{
		`button[type="submit"]`,
		`button[data-action-button-primary="true"]`,
		`button[name="action"]`,
		`input[type="submit"]`,
		`button:contains("Log in")`,
		`button:contains("Login")`,
		`button:contains("Sign in")`,
		`button:contains("登录")`,
		`button:contains("Sign In")`,
		`[data-testid="login-button"]`,
		`[data-testid="submit-button"]`,
		`.auth0-lock-submit`,
		`.auth0-lock-submit-button`,
	}

	for _, selector := range selectors {
		if ala.manager.IsElementPresent(selector) {
			logger.Debugf("找到登录按钮: %s", selector)
			if err := ala.manager.Click(selector); err != nil {
				logger.Debugf("点击按钮失败: %s, 错误: %v", selector, err)
				continue
			}
			logger.Info("登录按钮点击成功")
			return nil
		}
	}

	// 尝试使用JavaScript查找包含特定文本的按钮
	script := `
		const buttons = document.querySelectorAll('button, input[type="submit"]');
		for (let btn of buttons) {
			const text = btn.textContent || btn.value || '';
			if (text.toLowerCase().includes('log in') ||
				text.toLowerCase().includes('login') ||
				text.toLowerCase().includes('sign in') ||
				text.includes('登录') ||
				btn.type === 'submit') {
				btn.click();
				return { success: true, text: text };
			}
		}
		return { success: false };
	`

	var result map[string]interface{}
	if err := ala.manager.ExecuteScript(script, &result); err == nil {
		if success, ok := result["success"].(bool); ok && success {
			logger.Info("通过JavaScript点击登录按钮成功")
			return nil
		}
	}

	ala.manager.TakeScreenshot("login_button_not_found")
	return fmt.Errorf("未找到登录按钮")
}

// clearInput 清空输入框内容
func (ala *AugmentLoginAutomation) clearInput(selector string) error {
	script := fmt.Sprintf(`
		const element = document.querySelector('%s');
		if (element) {
			element.value = '';
			element.dispatchEvent(new Event('input', { bubbles: true }));
			element.dispatchEvent(new Event('change', { bubbles: true }));
			return true;
		}
		return false;
	`, selector)

	var result bool
	if err := ala.manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("执行清空脚本失败: %w", err)
	}

	if !result {
		return fmt.Errorf("未找到要清空的元素")
	}

	return nil
}

// checkLoginResult 检查登录结果
func (ala *AugmentLoginAutomation) checkLoginResult() error {
	logger.Info("检查登录结果")

	// 等待页面跳转或加载完成
	time.Sleep(3 * time.Second)

	// 检查是否登录成功 - 通过URL变化判断
	script := `
		return {
			url: window.location.href,
			title: document.title,
			hasError: !!document.querySelector('.error, .alert-error, [role="alert"]'),
			errorText: document.querySelector('.error, .alert-error, [role="alert"]')?.textContent || ''
		};
	`

	var result map[string]interface{}
	if err := ala.manager.ExecuteScript(script, &result); err != nil {
		return fmt.Errorf("获取页面信息失败: %w", err)
	}

	url := result["url"].(string)
	title := result["title"].(string)
	hasError := result["hasError"].(bool)
	errorText := result["errorText"].(string)

	logger.Infof("当前页面URL: %s", url)
	logger.Infof("当前页面标题: %s", title)

	// 检查是否有错误信息
	if hasError && strings.TrimSpace(errorText) != "" {
		ala.manager.TakeScreenshot("login_error")
		return fmt.Errorf("登录失败: %s", errorText)
	}

	// 检查是否还在登录页面（表示登录失败）
	if strings.Contains(url, "login.augmentcode.com") &&
		(strings.Contains(url, "/u/login") || strings.Contains(url, "identifier")) {
		ala.manager.TakeScreenshot("login_still_on_login_page")
		return fmt.Errorf("登录失败：仍在登录页面")
	}

	// 检查是否跳转到了成功页面
	if strings.Contains(url, "augmentcode.com") && !strings.Contains(url, "login.augmentcode.com") {
		logger.Info("登录成功：已跳转到主页面")
		ala.manager.TakeScreenshot("login_success")
		return nil
	}

	// 检查页面标题是否表示成功
	successTitles := []string{"dashboard", "home", "welcome", "augment"}
	for _, successTitle := range successTitles {
		if strings.Contains(strings.ToLower(title), successTitle) {
			logger.Info("登录成功：页面标题表示成功")
			ala.manager.TakeScreenshot("login_success")
			return nil
		}
	}

	// 如果无法确定，等待更长时间再检查
	logger.Info("登录状态不明确，等待更长时间...")
	time.Sleep(5 * time.Second)

	// 再次检查页面状态
	if err := ala.manager.ExecuteScript(script, &result); err == nil {
		newUrl := result["url"].(string)
		if newUrl != url {
			logger.Infof("页面URL已变化: %s -> %s", url, newUrl)
			if !strings.Contains(newUrl, "login.augmentcode.com") {
				logger.Info("登录成功：URL已跳转")
				ala.manager.TakeScreenshot("login_success_delayed")
				return nil
			}
		}
	}

	// 默认认为登录成功（避免误判）
	logger.Warn("无法确定登录状态，默认认为成功")
	ala.manager.TakeScreenshot("login_status_unknown")
	return nil
}

// LoginWithAccount 使用账号信息登录
func (ala *AugmentLoginAutomation) LoginWithAccount(account *generator.AccountInfo) error {
	return ala.LoginAccount(account.Email, account.Password)
}

// GetCurrentPageInfo 获取当前页面信息
func (ala *AugmentLoginAutomation) GetCurrentPageInfo() (map[string]string, error) {
	script := `
		return {
			url: window.location.href,
			title: document.title,
			domain: window.location.hostname,
			pathname: window.location.pathname,
			search: window.location.search
		};
	`

	var result map[string]interface{}
	if err := ala.manager.ExecuteScript(script, &result); err != nil {
		return nil, fmt.Errorf("获取页面信息失败: %w", err)
	}

	info := make(map[string]string)
	for key, value := range result {
		if str, ok := value.(string); ok {
			info[key] = str
		}
	}

	return info, nil
}

// WaitForPageLoad 等待页面加载完成
func (ala *AugmentLoginAutomation) WaitForPageLoad(timeout time.Duration) error {
	logger.Debug("等待页面加载完成")

	script := `
		return document.readyState === 'complete' &&
			   (!window.jQuery || window.jQuery.active === 0);
	`

	start := time.Now()
	for time.Since(start) < timeout {
		var ready bool
		if err := ala.manager.ExecuteScript(script, &ready); err == nil && ready {
			logger.Debug("页面加载完成")
			return nil
		}
		time.Sleep(500 * time.Millisecond)
	}

	logger.Warn("等待页面加载超时")
	return fmt.Errorf("页面加载超时")
}

// SavePageHTML 保存当前页面HTML用于调试
func (ala *AugmentLoginAutomation) SavePageHTML(filename string) error {
	script := `return document.documentElement.outerHTML;`

	var html string
	if err := ala.manager.ExecuteScript(script, &html); err != nil {
		return fmt.Errorf("获取页面HTML失败: %w", err)
	}

	// 创建调试目录
	debugDir := "debug"
	if err := os.MkdirAll(debugDir, 0755); err != nil {
		return fmt.Errorf("创建调试目录失败: %w", err)
	}

	// 保存HTML文件
	htmlFile := fmt.Sprintf("%s/%s.html", debugDir, filename)
	if err := os.WriteFile(htmlFile, []byte(html), 0644); err != nil {
		return fmt.Errorf("保存HTML文件失败: %w", err)
	}

	logger.Infof("页面HTML已保存: %s", htmlFile)
	return nil
}
