package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

// Config 配置结构体
type Config struct {
	// 自动化流程配置
	AutomationFlow string // 自动化流程: cursor_register, augment_login, custom
	FlowConfig     FlowConfig

	// 域名配置
	Domain string

	// 临时邮箱配置
	TempMail     string
	TempMailEpin string
	TempMailExt  string

	// IMAP配置
	IMAPServer   string
	IMAPPort     int
	IMAPUser     string
	IMAPPass     string
	IMAPDir      string
	IMAPProtocol string

	// 浏览器配置
	BrowserUserAgent string
	BrowserHeadless  bool
	BrowserPath      string
	BrowserProxy     string

	// 日志配置
	LogLevel string
	LogFile  string

	// 注册配置
	MaxRetries          int
	RetryInterval       int
	VerificationTimeout int
}

// FlowConfig 流程配置
type FlowConfig struct {
	// Cursor注册流程配置
	CursorBaseURL   string
	CursorDebugMode bool
	CursorQuickMode bool

	// Augment登录流程配置
	AugmentBaseURL   string
	AugmentDebugMode bool
	AugmentQuickMode bool

	// 通用流程配置
	EnableScreenshots bool
	ScreenshotDir     string
	MaxRetries        int
	RetryDelay        int // 重试延迟(秒)

	// 超时配置
	ElementWaitTimeout int // 等待元素出现超时(秒)
	PageLoadTimeout    int // 页面加载超时(秒)
	TurnstileTimeout   int // Turnstile验证超时(秒)
	InputTimeout       int // 输入操作超时(秒)
	ButtonTimeout      int // 按钮操作超时(秒)
	NavigationTimeout  int // 导航超时(秒)
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	// 加载 .env 文件
	if err := godotenv.Load(); err != nil {
		// .env 文件不存在时不报错，使用环境变量
		fmt.Println("Warning: .env file not found, using environment variables")
	}

	config := &Config{
		AutomationFlow:      getEnv("AUTOMATION_FLOW", "cursor_register"),
		Domain:              getEnv("DOMAIN", "example.com"),
		TempMail:            getEnv("TEMP_MAIL", "testuser"),
		TempMailEpin:        getEnv("TEMP_MAIL_EPIN", ""),
		TempMailExt:         getEnv("TEMP_MAIL_EXT", "@mailto.plus"),
		IMAPServer:          getEnv("IMAP_SERVER", ""),
		IMAPPort:            getEnvInt("IMAP_PORT", 993),
		IMAPUser:            getEnv("IMAP_USER", ""),
		IMAPPass:            getEnv("IMAP_PASS", ""),
		IMAPDir:             getEnv("IMAP_DIR", "inbox"),
		IMAPProtocol:        getEnv("IMAP_PROTOCOL", "IMAP"),
		BrowserUserAgent:    getEnv("BROWSER_USER_AGENT", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.92 Safari/537.36"),
		BrowserHeadless:     getEnvBool("BROWSER_HEADLESS", false),
		BrowserPath:         getEnv("BROWSER_PATH", ""),
		BrowserProxy:        getEnv("BROWSER_PROXY", ""),
		LogLevel:            getEnv("LOG_LEVEL", "debug"),
		LogFile:             getEnv("LOG_FILE", "auto-register.log"),
		MaxRetries:          getEnvInt("MAX_RETRIES", 3),
		RetryInterval:       getEnvInt("RETRY_INTERVAL", 60),
		VerificationTimeout: getEnvInt("VERIFICATION_TIMEOUT", 300),
		FlowConfig: FlowConfig{
			CursorBaseURL:     getEnv("FLOW_CURSOR_BASE_URL", "https://cursor.sh/sign-up"),
			CursorDebugMode:   getEnvBool("FLOW_CURSOR_DEBUG_MODE", false),
			CursorQuickMode:   getEnvBool("FLOW_CURSOR_QUICK_MODE", false),
			AugmentBaseURL:    getEnv("FLOW_AUGMENT_BASE_URL", "https://login.augmentcode.com/"),
			AugmentDebugMode:  getEnvBool("FLOW_AUGMENT_DEBUG_MODE", false),
			AugmentQuickMode:  getEnvBool("FLOW_AUGMENT_QUICK_MODE", false),
			EnableScreenshots: getEnvBool("FLOW_ENABLE_SCREENSHOTS", true),
			ScreenshotDir:     getEnv("FLOW_SCREENSHOT_DIR", "screenshots"),
			MaxRetries:        getEnvInt("FLOW_MAX_RETRIES", 3),
			RetryDelay:        getEnvInt("FLOW_RETRY_DELAY", 5),
			// 超时配置
			ElementWaitTimeout: getEnvInt("FLOW_ELEMENT_WAIT_TIMEOUT", 10),
			PageLoadTimeout:    getEnvInt("FLOW_PAGE_LOAD_TIMEOUT", 30),
			TurnstileTimeout:   getEnvInt("FLOW_TURNSTILE_TIMEOUT", 30),
			InputTimeout:       getEnvInt("FLOW_INPUT_TIMEOUT", 5),
			ButtonTimeout:      getEnvInt("FLOW_BUTTON_TIMEOUT", 5),
			NavigationTimeout:  getEnvInt("FLOW_NAVIGATION_TIMEOUT", 30),
		},
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Domain == "" {
		return fmt.Errorf("DOMAIN 配置不能为空")
	}

	// 检查邮箱配置
	if c.TempMail == "null" || c.TempMail == "" {
		// IMAP 模式
		if c.IMAPServer == "" {
			return fmt.Errorf("IMAP_SERVER 配置不能为空")
		}
		if c.IMAPUser == "" {
			return fmt.Errorf("IMAP_USER 配置不能为空")
		}
		if c.IMAPPass == "" {
			return fmt.Errorf("IMAP_PASS 配置不能为空")
		}
	} else {
		// 临时邮箱模式
		if c.TempMailEpin == "" {
			return fmt.Errorf("TEMP_MAIL_EPIN 配置不能为空")
		}
	}

	return nil
}

// IsIMAPMode 是否为IMAP模式
func (c *Config) IsIMAPMode() bool {
	return c.TempMail == "null" || c.TempMail == ""
}

// PrintConfig 打印配置信息
func (c *Config) PrintConfig() {
	fmt.Println("=== 配置信息 ===")
	fmt.Printf("自动化流程: %s\n", c.AutomationFlow)
	fmt.Printf("域名: %s\n", c.Domain)

	if c.IsIMAPMode() {
		fmt.Printf("邮箱模式: IMAP\n")
		fmt.Printf("IMAP服务器: %s:%d\n", c.IMAPServer, c.IMAPPort)
		fmt.Printf("IMAP用户: %s\n", c.IMAPUser)
		fmt.Printf("IMAP密码: %s\n", strings.Repeat("*", len(c.IMAPPass)))
		fmt.Printf("IMAP目录: %s\n", c.IMAPDir)
	} else {
		fmt.Printf("邮箱模式: 临时邮箱\n")
		fmt.Printf("临时邮箱: %s%s\n", c.TempMail, c.TempMailExt)
	}

	fmt.Printf("浏览器无头模式: %t\n", c.BrowserHeadless)
	fmt.Printf("日志级别: %s\n", c.LogLevel)

	// 打印流程配置
	fmt.Println("--- 流程配置 ---")
	switch c.AutomationFlow {
	case "cursor_register":
		fmt.Printf("Cursor注册URL: %s\n", c.FlowConfig.CursorBaseURL)
		fmt.Printf("Cursor调试模式: %t\n", c.FlowConfig.CursorDebugMode)
		fmt.Printf("Cursor快速模式: %t\n", c.FlowConfig.CursorQuickMode)
	case "augment_login":
		fmt.Printf("Augment登录URL: %s\n", c.FlowConfig.AugmentBaseURL)
		fmt.Printf("Augment调试模式: %t\n", c.FlowConfig.AugmentDebugMode)
		fmt.Printf("Augment快速模式: %t\n", c.FlowConfig.AugmentQuickMode)
	}
	fmt.Printf("启用截图: %t\n", c.FlowConfig.EnableScreenshots)
	fmt.Printf("截图目录: %s\n", c.FlowConfig.ScreenshotDir)
	fmt.Printf("最大重试次数: %d\n", c.FlowConfig.MaxRetries)
	fmt.Printf("重试延迟: %d秒\n", c.FlowConfig.RetryDelay)

	// 打印超时配置
	fmt.Println("--- 超时配置 ---")
	fmt.Printf("元素等待超时: %d秒\n", c.FlowConfig.ElementWaitTimeout)
	fmt.Printf("页面加载超时: %d秒\n", c.FlowConfig.PageLoadTimeout)
	fmt.Printf("Turnstile超时: %d秒\n", c.FlowConfig.TurnstileTimeout)
	fmt.Printf("输入操作超时: %d秒\n", c.FlowConfig.InputTimeout)
	fmt.Printf("按钮操作超时: %d秒\n", c.FlowConfig.ButtonTimeout)
	fmt.Printf("导航超时: %d秒\n", c.FlowConfig.NavigationTimeout)
	fmt.Println("================")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt 获取整型环境变量
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvBool 获取布尔型环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
