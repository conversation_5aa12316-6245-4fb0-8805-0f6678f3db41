package browser

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"auto-register/logger"
)

// saveScreenshot 保存截图
func saveScreenshot(buf []byte, filename string) error {
	// 创建截图目录
	screenshotDir := "screenshots"
	if err := os.Mkdir<PERSON>ll(screenshotDir, 0755); err != nil {
		return fmt.Errorf("创建截图目录失败: %w", err)
	}

	// 如果没有指定文件名，使用时间戳
	if filename == "" {
		filename = fmt.Sprintf("screenshot_%d.png", time.Now().Unix())
	}

	// 确保文件扩展名为.png
	if filepath.Ext(filename) == "" {
		filename += ".png"
	}

	filepath := filepath.Join(screenshotDir, filename)

	// 写入文件
	if err := os.WriteFile(filepath, buf, 0644); err != nil {
		return fmt.Errorf("保存截图失败: %w", err)
	}

	logger.Infof("截图已保存: %s", filepath)
	return nil
}

// RandomDelay 随机延迟
func RandomDelay(min, max int) {
	if min >= max {
		return
	}

	delay := time.Duration(min+int(time.Now().UnixNano())%(max-min)) * time.Second
	time.Sleep(delay)
}

// HumanLikeDelay 模拟人类操作的延迟
func HumanLikeDelay() {
	// 随机延迟 1-3 秒
	RandomDelay(1, 3)
}

// SilentWriter 静默写入器，用于过滤chromedp的错误日志
type SilentWriter struct{}

// Write 实现io.Writer接口，过滤掉chromedp的cookie解析错误
func (sw *SilentWriter) Write(p []byte) (n int, err error) {
	msg := string(p)
	// 过滤掉chromedp的cookie解析错误和其他噪音日志
	if strings.Contains(msg, "could not unmarshal event") ||
		strings.Contains(msg, "cookiePart") ||
		strings.Contains(msg, "unknown ClientNavigationReason") {
		return len(p), nil // 静默丢弃这些错误
	}
	// 其他错误仍然输出到stderr
	return os.Stderr.Write(p)
}

// NewSilentWriter 创建新的静默写入器
func NewSilentWriter() *SilentWriter {
	return &SilentWriter{}
}
