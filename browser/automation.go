package browser

import (
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"

	"auto-register/generator"
	"auto-register/logger"
)

// RegistrationAutomation 注册自动化
type RegistrationAutomation struct {
	manager *Manager
}

// NewRegistrationAutomation 创建注册自动化实例
func NewRegistrationAutomation(manager *Manager) *RegistrationAutomation {
	return &RegistrationAutomation{
		manager: manager,
	}
}

// RegisterAccount 自动注册账号
func (ra *RegistrationAutomation) RegisterAccount(account *generator.AccountInfo) error {
	logger.Info("开始自动注册账号")

	// 导航到注册页面
	signUpURL := "https://authenticator.cursor.sh/sign-up"
	if err := ra.manager.Navigate(signUpURL); err != nil {
		return fmt.Errorf("导航到注册页面失败: %w", err)
	}

	// 等待页面加载
	time.Sleep(2 * time.Second)

	// 填写个人信息
	if err := ra.fillPersonalInfo(account); err != nil {
		return fmt.Errorf("填写个人信息失败: %w", err)
	}

	// 处理验证码
	if err := ra.handleTurnstile(); err != nil {
		return fmt.Errorf("处理验证码失败: %w", err)
	}

	// 设置密码
	if err := ra.setPassword(account.Password); err != nil {
		return fmt.Errorf("设置密码失败: %w", err)
	}

	// 处理邮箱验证
	if err := ra.handleEmailVerification(); err != nil {
		return fmt.Errorf("处理邮箱验证失败: %w", err)
	}

	logger.Info("账号注册完成")
	return nil
}

// fillPersonalInfo 填写个人信息
func (ra *RegistrationAutomation) fillPersonalInfo(account *generator.AccountInfo) error {
	logger.Info("填写个人信息")

	// 等待表单元素出现
	if err := ra.manager.WaitForElement(`input[name="first_name"]`, 10*time.Second); err != nil {
		return fmt.Errorf("等待姓名输入框失败: %w", err)
	}

	// 填写名字
	if err := ra.manager.SendKeys(`input[name="first_name"]`, account.FirstName); err != nil {
		return fmt.Errorf("输入名字失败: %w", err)
	}
	logger.Infof("输入名字: %s", account.FirstName)
	HumanLikeDelay()

	// 填写姓氏
	if err := ra.manager.SendKeys(`input[name="last_name"]`, account.LastName); err != nil {
		return fmt.Errorf("输入姓氏失败: %w", err)
	}
	logger.Infof("输入姓氏: %s", account.LastName)
	HumanLikeDelay()

	// 填写邮箱
	if err := ra.manager.SendKeys(`input[name="email"]`, account.Email); err != nil {
		return fmt.Errorf("输入邮箱失败: %w", err)
	}
	logger.Infof("输入邮箱: %s", account.Email)
	HumanLikeDelay()

	// 提交表单
	if err := ra.manager.Click(`button[type="submit"]`); err != nil {
		return fmt.Errorf("提交个人信息失败: %w", err)
	}
	logger.Info("提交个人信息")

	return nil
}

// handleTurnstile 处理Turnstile验证码
func (ra *RegistrationAutomation) handleTurnstile() error {
	logger.Info("检测Turnstile验证码")

	// 保存截图
	ra.manager.TakeScreenshot("turnstile_start")

	// 首先等待页面完全加载
	time.Sleep(3 * time.Second)

	// 检查是否已经通过验证
	if ra.isVerificationSuccessful() {
		logger.Info("页面已通过验证，无需处理Turnstile")
		return nil
	}

	// 等待Turnstile元素出现
	if !ra.waitForTurnstileElement(10 * time.Second) {
		logger.Warn("未检测到Turnstile元素，可能已经通过验证")
		return nil
	}

	maxRetries := 5
	for i := 0; i < maxRetries; i++ {
		logger.Infof("尝试验证 (第 %d/%d 次)", i+1, maxRetries)

		// 尝试多种方法处理Turnstile
		if ra.tryTurnstileMethod1() || ra.tryTurnstileMethod2() || ra.tryTurnstileMethod3() || ra.tryTurnstileMethod4() {
			logger.Info("Turnstile验证码处理成功")
			ra.manager.TakeScreenshot("turnstile_clicked")

			// 等待验证完成，最多等待10秒
			if ra.waitForTurnstileCompletion(10 * time.Second) {
				logger.Info("Turnstile验证通过")
				ra.manager.TakeScreenshot("turnstile_success")
				return nil
			}
		}

		// 等待更长时间后重试
		delay := time.Duration(3+rand.Intn(3)) * time.Second
		logger.Infof("等待 %v 后重试", delay)
		time.Sleep(delay)

		// 保存重试前的截图
		ra.manager.TakeScreenshot(fmt.Sprintf("turnstile_retry_%d", i+1))
	}

	logger.Error("Turnstile验证失败，已达最大重试次数")
	ra.manager.TakeScreenshot("turnstile_failed")

	// 保存页面HTML用于调试
	ra.savePageHTML("turnstile_failed")

	return fmt.Errorf("Turnstile验证失败")
}

// isVerificationSuccessful 检查验证是否成功
func (ra *RegistrationAutomation) isVerificationSuccessful() bool {
	// 检查是否出现密码页面
	if ra.manager.IsElementPresent(`input[name="password"]`) {
		logger.Debug("检测到密码输入页面")
		return true
	}

	// 检查是否出现验证码输入页面
	if ra.manager.IsElementPresent(`input[data-index="0"]`) {
		logger.Debug("检测到验证码输入页面")
		return true
	}

	// 检查是否已经到达账户设置页面
	if ra.manager.IsElementPresent(`text="Account Settings"`) {
		logger.Debug("检测到账户设置页面")
		return true
	}

	// 检查是否有提交按钮可用（表示验证通过）
	script := `
		try {
			// 检查提交按钮状态
			const submitBtn = document.querySelector('button[type="submit"]');
			if (submitBtn && !submitBtn.disabled) {
				return { success: true, reason: 'submit_button_enabled' };
			}

			// 检查Turnstile状态
			const turnstile = document.querySelector('#cf-turnstile');
			if (turnstile) {
				const iframe = turnstile.querySelector('iframe');
				if (iframe) {
					// 检查iframe是否隐藏
					const iframeStyle = iframe.style.display;
					if (iframeStyle === 'none') {
						return { success: true, reason: 'iframe_hidden' };
					}

					// 检查iframe内容
					try {
						const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
						if (iframeDoc) {
							// 查找成功标识
							const successElements = iframeDoc.querySelectorAll('[data-success], .success, .verified');
							if (successElements.length > 0) {
								return { success: true, reason: 'success_element_found' };
							}

							// 检查复选框状态
							const checkbox = iframeDoc.querySelector('input[type="checkbox"]');
							if (checkbox && checkbox.checked) {
								return { success: true, reason: 'checkbox_checked' };
							}
						}
					} catch(e) {
						// iframe访问被阻止，可能是跨域问题
					}
				}

				// 检查Turnstile容器的class
				if (turnstile.classList.contains('success') ||
					turnstile.classList.contains('verified') ||
					turnstile.classList.contains('completed')) {
					return { success: true, reason: 'turnstile_class_success' };
				}
			}

			// 检查页面URL变化
			if (window.location.href.includes('password') ||
				window.location.href.includes('verify') ||
				window.location.href.includes('complete')) {
				return { success: true, reason: 'url_changed' };
			}

			return { success: false, reason: 'no_success_indicators' };
		} catch(e) {
			return { success: false, reason: 'exception', error: e.message };
		}
	`

	var result map[string]interface{}
	if err := ra.manager.ExecuteScript(script, &result); err == nil {
		if success, ok := result["success"].(bool); ok && success {
			reason := result["reason"].(string)
			logger.Debugf("JavaScript检测验证成功: %s", reason)
			return true
		}
	}

	return false
}

// setPassword 设置密码
func (ra *RegistrationAutomation) setPassword(password string) error {
	logger.Info("设置密码")

	// 等待密码输入框出现
	if err := ra.manager.WaitForElement(`input[name="password"]`, 10*time.Second); err != nil {
		return fmt.Errorf("等待密码输入框失败: %w", err)
	}

	// 输入密码
	if err := ra.manager.SendKeys(`input[name="password"]`, password); err != nil {
		return fmt.Errorf("输入密码失败: %w", err)
	}
	logger.Info("密码输入完成")
	HumanLikeDelay()

	// 提交密码
	if err := ra.manager.Click(`button[type="submit"]`); err != nil {
		return fmt.Errorf("提交密码失败: %w", err)
	}
	logger.Info("密码设置完成")

	return nil
}

// handleEmailVerification 处理邮箱验证
func (ra *RegistrationAutomation) handleEmailVerification() error {
	logger.Info("处理邮箱验证")

	// 检查是否需要邮箱验证
	if !ra.manager.IsElementPresent(`input[data-index="0"]`) {
		logger.Info("无需邮箱验证")
		return nil
	}

	logger.Info("需要邮箱验证码，请手动处理或实现邮箱验证逻辑")

	// 这里应该集成邮箱验证逻辑
	// 暂时返回错误，提示需要手动处理
	return fmt.Errorf("需要邮箱验证码，请手动处理")
}

// GetSessionToken 获取会话令牌
func (ra *RegistrationAutomation) GetSessionToken() (string, error) {
	logger.Info("获取会话令牌")

	script := `
		const cookies = document.cookie.split(';');
		for (let cookie of cookies) {
			const [name, value] = cookie.trim().split('=');
			if (name === 'WorkosCursorSessionToken') {
				const parts = decodeURIComponent(value).split('::');
				if (parts.length > 1) {
					return parts[1];
				}
			}
		}
		return null;
	`

	var token string
	if err := ra.manager.ExecuteScript(script, &token); err != nil {
		return "", fmt.Errorf("获取会话令牌失败: %w", err)
	}

	if token == "" {
		return "", fmt.Errorf("未找到会话令牌")
	}

	logger.Infof("获取到会话令牌: %s", token[:10]+"...")
	return token, nil
}

// CheckRegistrationResult 检查注册结果
func (ra *RegistrationAutomation) CheckRegistrationResult() error {
	// 检查是否注册成功
	if ra.manager.IsElementPresent(`text="Account Settings"`) {
		logger.Info("注册成功，已到达账户设置页面")
		return nil
	}

	// 检查是否有错误信息
	if ra.manager.IsElementPresent(`text="This email is not available."`) {
		return fmt.Errorf("注册失败：邮箱已被使用")
	}

	// 检查其他可能的错误
	errorSelectors := []string{
		`[role="alert"]`,
		`.error`,
		`.alert-error`,
	}

	for _, selector := range errorSelectors {
		if ra.manager.IsElementPresent(selector) {
			if text, err := ra.manager.GetText(selector); err == nil && strings.TrimSpace(text) != "" {
				return fmt.Errorf("注册失败：%s", text)
			}
		}
	}

	return nil
}

// tryTurnstileMethod1 方法1：直接点击iframe中的复选框
func (ra *RegistrationAutomation) tryTurnstileMethod1() bool {
	logger.Debug("尝试方法1：直接点击iframe复选框")

	script := `
		try {
			const turnstile = document.querySelector('#cf-turnstile');
			if (!turnstile) return { success: false, reason: 'no_turnstile' };

			const iframe = turnstile.querySelector('iframe');
			if (!iframe) return { success: false, reason: 'no_iframe' };

			// 等待iframe加载
			if (!iframe.contentDocument && !iframe.contentWindow) {
				return { success: false, reason: 'iframe_not_loaded' };
			}

			const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
			if (!iframeDoc) return { success: false, reason: 'no_iframe_doc' };

			// 查找复选框
			const checkbox = iframeDoc.querySelector('input[type="checkbox"]');
			if (checkbox) {
				if (!checkbox.checked) {
					checkbox.click();
					return { success: true, reason: 'checkbox_clicked' };
				} else {
					return { success: true, reason: 'already_checked' };
				}
			}

			// 查找其他可点击元素
			const clickableElements = iframeDoc.querySelectorAll('div, span, button');
			for (let elem of clickableElements) {
				if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
					elem.click();
					return { success: true, reason: 'element_clicked' };
				}
			}

			return { success: false, reason: 'no_clickable_elements' };
		} catch(e) {
			return { success: false, reason: 'exception', error: e.message };
		}
	`

	var result map[string]interface{}
	if err := ra.manager.ExecuteScript(script, &result); err == nil {
		if success, ok := result["success"].(bool); ok && success {
			reason := result["reason"].(string)
			logger.Debugf("方法1成功: %s", reason)
			return true
		} else {
			reason := result["reason"].(string)
			logger.Debugf("方法1失败: %s", reason)
		}
	}
	return false
}

// tryTurnstileMethod2 方法2：点击iframe元素和容器
func (ra *RegistrationAutomation) tryTurnstileMethod2() bool {
	logger.Debug("尝试方法2：点击iframe元素和容器")

	script := `
		try {
			const turnstile = document.querySelector('#cf-turnstile');
			if (!turnstile) return { success: false, reason: 'no_turnstile' };

			// 先尝试点击容器
			turnstile.click();

			const iframe = turnstile.querySelector('iframe');
			if (iframe) {
				// 模拟鼠标悬停
				const hoverEvent = new MouseEvent('mouseover', {
					view: window,
					bubbles: true,
					cancelable: true
				});
				iframe.dispatchEvent(hoverEvent);

				// 点击iframe
				const clickEvent = new MouseEvent('click', {
					view: window,
					bubbles: true,
					cancelable: true
				});
				iframe.dispatchEvent(clickEvent);

				// 尝试focus
				iframe.focus();

				return { success: true, reason: 'iframe_clicked' };
			}

			return { success: false, reason: 'no_iframe' };
		} catch(e) {
			return { success: false, reason: 'exception', error: e.message };
		}
	`

	var result map[string]interface{}
	if err := ra.manager.ExecuteScript(script, &result); err == nil {
		if success, ok := result["success"].(bool); ok && success {
			reason := result["reason"].(string)
			logger.Debugf("方法2成功: %s", reason)
			return true
		} else {
			reason := result["reason"].(string)
			logger.Debugf("方法2失败: %s", reason)
		}
	}
	return false
}

// tryTurnstileMethod3 方法3：模拟用户交互
func (ra *RegistrationAutomation) tryTurnstileMethod3() bool {
	logger.Debug("尝试方法3：模拟用户交互")

	script := `
		try {
			const turnstile = document.querySelector('#cf-turnstile');
			if (turnstile) {
				const iframe = turnstile.querySelector('iframe');
				if (iframe) {
					// 模拟鼠标悬停和点击
					const event = new MouseEvent('click', {
						view: window,
						bubbles: true,
						cancelable: true
					});
					iframe.dispatchEvent(event);

					// 尝试触发iframe内的点击
					try {
						const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
						const clickableElements = iframeDoc.querySelectorAll('*');
						for (let elem of clickableElements) {
							if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
								elem.click();
								break;
							}
						}
					} catch(e) {
						console.log('Inner iframe click failed:', e);
					}

					return true;
				}
			}
			return false;
		} catch(e) {
			console.log('Method 3 error:', e);
			return false;
		}
	`

	var result bool
	if err := ra.manager.ExecuteScript(script, &result); err == nil && result {
		logger.Debug("方法3成功")
		return true
	}
	return false
}

// waitForTurnstileCompletion 等待Turnstile验证完成
func (ra *RegistrationAutomation) waitForTurnstileCompletion(timeout time.Duration) bool {
	logger.Infof("等待Turnstile验证完成，超时时间: %v", timeout)

	start := time.Now()
	for time.Since(start) < timeout {
		if ra.isVerificationSuccessful() {
			logger.Info("Turnstile验证完成")
			return true
		}

		// 检查是否有错误信息
		script := `
			try {
				const errorElements = document.querySelectorAll('.error, .alert, [role="alert"]');
				for (let elem of errorElements) {
					if (elem.textContent && elem.textContent.trim()) {
						return elem.textContent.trim();
					}
				}
				return '';
			} catch(e) {
				return '';
			}
		`

		var errorMsg string
		if err := ra.manager.ExecuteScript(script, &errorMsg); err == nil && errorMsg != "" {
			logger.Warnf("检测到错误信息: %s", errorMsg)
		}

		time.Sleep(1 * time.Second)
	}

	logger.Warn("等待Turnstile验证超时")
	return false
}

// savePageHTML 保存页面HTML用于调试
func (ra *RegistrationAutomation) savePageHTML(filename string) {
	script := `return document.documentElement.outerHTML;`

	var html string
	if err := ra.manager.ExecuteScript(script, &html); err != nil {
		logger.Errorf("获取页面HTML失败: %v", err)
		return
	}

	// 创建调试目录
	debugDir := "debug"
	if err := os.MkdirAll(debugDir, 0755); err != nil {
		logger.Errorf("创建调试目录失败: %v", err)
		return
	}

	// 保存HTML文件
	htmlFile := fmt.Sprintf("%s/%s.html", debugDir, filename)
	if err := os.WriteFile(htmlFile, []byte(html), 0644); err != nil {
		logger.Errorf("保存HTML文件失败: %v", err)
		return
	}

	logger.Infof("页面HTML已保存: %s", htmlFile)
}

// waitForTurnstileElement 等待Turnstile元素出现
func (ra *RegistrationAutomation) waitForTurnstileElement(timeout time.Duration) bool {
	logger.Debug("等待Turnstile元素出现")

	start := time.Now()
	for time.Since(start) < timeout {
		script := `
			try {
				const turnstile = document.querySelector('#cf-turnstile');
				if (turnstile) {
					const iframe = turnstile.querySelector('iframe');
					return {
						found: true,
						hasIframe: !!iframe,
						iframeLoaded: iframe ? iframe.contentDocument !== null : false
					};
				}
				return { found: false };
			} catch(e) {
				return { found: false, error: e.message };
			}
		`

		var result map[string]interface{}
		if err := ra.manager.ExecuteScript(script, &result); err == nil {
			if found, ok := result["found"].(bool); ok && found {
				logger.Debug("Turnstile元素已找到")
				if hasIframe, ok := result["hasIframe"].(bool); ok && hasIframe {
					logger.Debug("Turnstile iframe已加载")
					return true
				}
			}
		}

		time.Sleep(500 * time.Millisecond)
	}

	logger.Debug("等待Turnstile元素超时")
	return false
}

// tryTurnstileMethod4 方法4：使用chromedp原生点击
func (ra *RegistrationAutomation) tryTurnstileMethod4() bool {
	logger.Debug("尝试方法4：使用chromedp原生点击")

	// 尝试使用chromedp的原生点击功能
	selectors := []string{
		"#cf-turnstile iframe",
		"#cf-turnstile",
		"[data-sitekey]",
		".cf-turnstile",
	}

	for _, selector := range selectors {
		if ra.manager.IsElementPresent(selector) {
			logger.Debugf("找到元素: %s", selector)
			if err := ra.manager.Click(selector); err == nil {
				logger.Debugf("方法4成功点击: %s", selector)
				return true
			} else {
				logger.Debugf("点击失败: %s, 错误: %v", selector, err)
			}
		}
	}

	logger.Debug("方法4失败：未找到可点击的元素")
	return false
}
