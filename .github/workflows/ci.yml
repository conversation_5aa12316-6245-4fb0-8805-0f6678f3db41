name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install dependencies
      run: go mod download
    
    - name: Run tests
      run: go test -v ./...
    
    - name: Run go vet
      run: go vet ./...
    
    - name: Run go fmt
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Code is not formatted properly:"
          gofmt -s -l .
          exit 1
        fi
    
    - name: Build
      run: go build -v ./...

  build:
    runs-on: ubuntu-latest
    needs: test
    
    strategy:
      matrix:
        goos: [linux, windows, darwin]
        goarch: [amd64, arm64]
        exclude:
          - goos: windows
            goarch: arm64
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
    
    - name: Build binary
      env:
        GOOS: ${{ matrix.goos }}
        GOARCH: ${{ matrix.goarch }}
      run: |
        mkdir -p bin
        if [ "$GOOS" = "windows" ]; then
          go build -o bin/auto-register-${{ matrix.goos }}-${{ matrix.goarch }}.exe main.go
        else
          go build -o bin/auto-register-${{ matrix.goos }}-${{ matrix.goarch }} main.go
        fi
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: auto-register-${{ matrix.goos }}-${{ matrix.goarch }}
        path: bin/
