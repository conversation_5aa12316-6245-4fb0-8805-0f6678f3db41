package step

import (
	"fmt"

	"auto-register/browser/automation/action"
)

// Step 步骤接口
type Step interface {
	Execute(ctx *action.ActionContext) error
	GetName() string
}

// StepSequence 步骤序列
type StepSequence struct {
	Name        string
	Steps       []Step
	StopOnError bool
}

// NewStepSequence 创建步骤序列
func NewStepSequence(name string) *StepSequence {
	return &StepSequence{
		Name:        name,
		Steps:       make([]Step, 0),
		StopOnError: true,
	}
}

// WithStopOnError 设置遇到错误时是否停止
func (s *StepSequence) WithStopOnError(stop bool) *StepSequence {
	s.StopOnError = stop
	return s
}

// Add 添加步骤到序列
func (s *StepSequence) Add(step Step) *StepSequence {
	s.Steps = append(s.Steps, step)
	return s
}

// AddMultiple 添加多个步骤到序列
func (s *StepSequence) AddMultiple(steps ...Step) *StepSequence {
	s.Steps = append(s.Steps, steps...)
	return s
}

// Execute 执行步骤序列
func (s *StepSequence) Execute(ctx *action.ActionContext) error {
	for i, step := range s.Steps {
		if err := step.Execute(ctx); err != nil {
			if s.StopOnError {
				return fmt.Errorf("步骤序列在第 %d 步失败: %w", i+1, err)
			}
		}
	}
	return nil
}

// GetName 获取步骤名称
func (s *StepSequence) GetName() string {
	return fmt.Sprintf("StepSequence[%s]", s.Name)
}
