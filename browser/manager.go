package browser

import (
	"context"
	"fmt"
	"log"
	"time"

	"auto-register/config"
	"auto-register/logger"

	"github.com/chromedp/chromedp"
)

// Manager 浏览器管理器
type Manager struct {
	config *config.Config
	ctx    context.Context
	cancel context.CancelFunc
}

// NewManager 创建新的浏览器管理器
func NewManager(cfg *config.Config) *Manager {
	return &Manager{
		config: cfg,
	}
}

// Start 启动浏览器
func (m *Manager) Start() error {
	// 创建一个静默的日志输出来减少chromedp的错误日志噪音
	log.SetOutput(NewSilentWriter())

	// 创建浏览器选项
	opts := []chromedp.ExecAllocatorOption{
		chromedp.Flag("headless", m.config.BrowserHeadless),
		chromedp.Flag("disable-gpu", true),
		// chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-extensions", false),
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("allow-running-insecure-content", true),
		chromedp.Flag("disable-logging", true),
		chromedp.Flag("silent", true),
		chromedp.Flag("disable-background-timer-throttling", true),
		chromedp.Flag("disable-renderer-backgrounding", true),
		chromedp.Flag("disable-backgrounding-occluded-windows", true),
		chromedp.UserAgent(m.config.BrowserUserAgent),
	}

	// 如果指定了浏览器路径
	if m.config.BrowserPath != "" {
		opts = append(opts, chromedp.ExecPath(m.config.BrowserPath))
	}

	// 如果指定了代理
	if m.config.BrowserProxy != "" {
		opts = append(opts, chromedp.ProxyServer(m.config.BrowserProxy))
	}

	// 创建浏览器上下文
	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	m.ctx, m.cancel = chromedp.NewContext(allocCtx)

	// 启动浏览器
	if err := chromedp.Run(m.ctx); err != nil {
		return fmt.Errorf("启动浏览器失败: %w", err)
	}

	logger.Info("浏览器启动成功")
	return nil
}

// Stop 停止浏览器
func (m *Manager) Stop() {
	if m.cancel != nil {
		m.cancel()
		logger.Info("浏览器已关闭")
	}
}

// GetContext 获取浏览器上下文
func (m *Manager) GetContext() context.Context {
	return m.ctx
}

// Navigate 导航到指定URL
func (m *Manager) Navigate(url string) error {
	logger.Infof("导航到: %s", url)
	return chromedp.Run(m.ctx, chromedp.Navigate(url))
}

// WaitVisible 等待元素可见
func (m *Manager) WaitVisible(selector string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(m.ctx, timeout)
	defer cancel()

	return chromedp.Run(ctx, chromedp.WaitVisible(selector))
}

// Click 点击元素
func (m *Manager) Click(selector string) error {
	return chromedp.Run(m.ctx, chromedp.Click(selector))
}

// SendKeys 输入文本
func (m *Manager) SendKeys(selector, text string) error {
	return chromedp.Run(m.ctx, chromedp.SendKeys(selector, text))
}

// GetText 获取元素文本
func (m *Manager) GetText(selector string) (string, error) {
	var text string
	err := chromedp.Run(m.ctx, chromedp.Text(selector, &text))
	return text, err
}

// GetAttribute 获取元素属性
func (m *Manager) GetAttribute(selector, attribute string) (string, error) {
	var value string
	err := chromedp.Run(m.ctx, chromedp.AttributeValue(selector, attribute, &value, nil))
	return value, err
}

// ExecuteScript 执行JavaScript
func (m *Manager) ExecuteScript(script string, res interface{}) error {
	return chromedp.Run(m.ctx, chromedp.Evaluate(script, res))
}

// TakeScreenshot 截图
func (m *Manager) TakeScreenshot(filename string) error {
	var buf []byte
	if err := chromedp.Run(m.ctx, chromedp.CaptureScreenshot(&buf)); err != nil {
		return err
	}

	// 保存截图
	return saveScreenshot(buf, filename)
}

// WaitForNavigation 等待页面导航完成
func (m *Manager) WaitForNavigation(timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(m.ctx, timeout)
	defer cancel()

	return chromedp.Run(ctx, chromedp.WaitReady("body"))
}

// IsElementPresent 检查元素是否存在
func (m *Manager) IsElementPresent(selector string) bool {
	ctx, cancel := context.WithTimeout(m.ctx, 1*time.Second)
	defer cancel()

	err := chromedp.Run(ctx, chromedp.WaitVisible(selector))
	return err == nil
}

// WaitForElement 等待元素出现
func (m *Manager) WaitForElement(selector string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(m.ctx, timeout)
	defer cancel()

	return chromedp.Run(ctx, chromedp.WaitVisible(selector))
}
