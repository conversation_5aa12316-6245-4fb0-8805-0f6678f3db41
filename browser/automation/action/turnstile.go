package action

import (
	"fmt"
	"time"

	"auto-register/logger"
)

// TurnstileAction Turnstile验证操作
type TurnstileAction struct {
	WaitTimeout    time.Duration
	MaxRetries     int
	RetryInterval  time.Duration
	TakeScreenshot bool
	ScreenshotName string
}

// NewTurnstileAction 创建Turnstile验证操作
func NewTurnstileAction() *TurnstileAction {
	return &TurnstileAction{
		WaitTimeout:    30 * time.Second,
		MaxRetries:     3,
		RetryInterval:  5 * time.Second,
		TakeScreenshot: true,
		ScreenshotName: "turnstile_completed",
	}
}

// WithTimeout 设置等待超时时间
func (a *TurnstileAction) WithTimeout(timeout time.Duration) *TurnstileAction {
	a.WaitTimeout = timeout
	return a
}

// WithRetries 设置重试选项
func (a *TurnstileAction) WithRetries(maxRetries int, interval time.Duration) *TurnstileAction {
	a.MaxRetries = maxRetries
	a.RetryInterval = interval
	return a
}

// WithScreenshot 设置截图选项
func (a *TurnstileAction) WithScreenshot(take bool, name string) *TurnstileAction {
	a.TakeScreenshot = take
	a.ScreenshotName = name
	return a
}

// Execute 执行Turnstile验证操作
func (a *TurnstileAction) Execute(ctx *ActionContext) error {
	logger.Info("开始处理Turnstile验证")

	for retry := 0; retry <= a.MaxRetries; retry++ {
		if retry > 0 {
			logger.Infof("重试Turnstile验证 (第 %d/%d 次)", retry, a.MaxRetries)
			time.Sleep(a.RetryInterval)
		}

		// 检查是否存在Turnstile
		if !a.isTurnstilePresent(ctx) {
			logger.Debug("未检测到Turnstile验证")
			return nil
		}

		logger.Info("检测到Turnstile验证，等待完成...")

		// 等待Turnstile完成
		if err := a.waitForTurnstileCompletion(ctx); err != nil {
			logger.Warnf("Turnstile验证失败: %v", err)
			if retry == a.MaxRetries {
				return fmt.Errorf("Turnstile验证失败，已达最大重试次数: %w", err)
			}
			continue
		}

		logger.Info("Turnstile验证完成")

		// 截图
		if a.TakeScreenshot {
			if err := ctx.Manager.TakeScreenshot(a.ScreenshotName); err != nil {
				logger.Warnf("截图失败: %v", err)
			}
		}

		return nil
	}

	return fmt.Errorf("Turnstile验证失败，已达最大重试次数")
}

// isTurnstilePresent 检查是否存在Turnstile
func (a *TurnstileAction) isTurnstilePresent(ctx *ActionContext) bool {
	selectors := []string{
		`iframe[src*="turnstile"]`,
		`iframe[src*="cloudflare"]`,
		`.cf-turnstile`,
		`[data-sitekey]`,
		`div[id*="turnstile"]`,
		`div[class*="turnstile"]`,
	}

	for _, selector := range selectors {
		if ctx.Manager.IsElementPresent(selector) {
			logger.Debugf("找到Turnstile元素: %s", selector)
			return true
		}
	}

	return false
}

// waitForTurnstileCompletion 等待Turnstile完成
func (a *TurnstileAction) waitForTurnstileCompletion(ctx *ActionContext) error {
	// 等待Turnstile iframe加载
	time.Sleep(2 * time.Second)

	// 检查Turnstile状态的脚本
	script := `
		// 检查Turnstile是否完成
		function checkTurnstileStatus() {
			// 查找Turnstile相关元素
			const turnstileElements = document.querySelectorAll('iframe[src*="turnstile"], iframe[src*="cloudflare"], .cf-turnstile, [data-sitekey]');
			
			if (turnstileElements.length === 0) {
				return { status: 'not_found', message: '未找到Turnstile元素' };
			}

			// 检查是否有成功标识
			const successElements = document.querySelectorAll('.cf-turnstile-success, [data-cf-turnstile-success="true"]');
			if (successElements.length > 0) {
				return { status: 'success', message: 'Turnstile验证成功' };
			}

			// 检查是否有错误标识
			const errorElements = document.querySelectorAll('.cf-turnstile-error, [data-cf-turnstile-error="true"]');
			if (errorElements.length > 0) {
				return { status: 'error', message: 'Turnstile验证失败' };
			}

			// 检查iframe内容（如果可访问）
			try {
				for (let iframe of turnstileElements) {
					if (iframe.tagName === 'IFRAME') {
						// 检查iframe是否已加载
						if (iframe.contentDocument) {
							const iframeDoc = iframe.contentDocument;
							const successCheck = iframeDoc.querySelector('[data-success="true"], .success');
							if (successCheck) {
								return { status: 'success', message: 'Turnstile iframe验证成功' };
							}
						}
					}
				}
			} catch (e) {
				// iframe跨域访问限制，这是正常的
			}

			return { status: 'pending', message: 'Turnstile验证进行中' };
		}

		return checkTurnstileStatus();
	`

	start := time.Now()
	for time.Since(start) < a.WaitTimeout {
		var result map[string]interface{}
		if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
			logger.Warnf("执行Turnstile检查脚本失败: %v", err)
			time.Sleep(2 * time.Second)
			continue
		}

		status := result["status"].(string)
		message := result["message"].(string)

		logger.Debugf("Turnstile状态: %s - %s", status, message)

		switch status {
		case "success":
			return nil
		case "error":
			return fmt.Errorf("Turnstile验证失败: %s", message)
		case "not_found":
			// Turnstile可能已经消失，认为验证完成
			return nil
		case "pending":
			// 继续等待
			time.Sleep(2 * time.Second)
			continue
		}
	}

	return fmt.Errorf("Turnstile验证超时")
}

// GetName 获取操作名称
func (a *TurnstileAction) GetName() string {
	return "Turnstile"
}

// WaitForTurnstileAction 等待Turnstile出现操作
type WaitForTurnstileAction struct {
	WaitTimeout time.Duration
	Required    bool
}

// NewWaitForTurnstileAction 创建等待Turnstile出现操作
func NewWaitForTurnstileAction() *WaitForTurnstileAction {
	return &WaitForTurnstileAction{
		WaitTimeout: 10 * time.Second,
		Required:    false,
	}
}

// WithTimeout 设置超时时间
func (a *WaitForTurnstileAction) WithTimeout(timeout time.Duration) *WaitForTurnstileAction {
	a.WaitTimeout = timeout
	return a
}

// WithRequired 设置是否必需
func (a *WaitForTurnstileAction) WithRequired(required bool) *WaitForTurnstileAction {
	a.Required = required
	return a
}

// Execute 执行等待Turnstile出现操作
func (a *WaitForTurnstileAction) Execute(ctx *ActionContext) error {
	logger.Debug("等待Turnstile出现")

	selectors := ElementSelectors{
		`iframe[src*="turnstile"]`,
		`iframe[src*="cloudflare"]`,
		`.cf-turnstile`,
		`[data-sitekey]`,
		`div[id*="turnstile"]`,
		`div[class*="turnstile"]`,
	}

	_, err := selectors.FindElement(ctx, a.WaitTimeout)
	if err != nil {
		if a.Required {
			return fmt.Errorf("等待Turnstile出现失败: %w", err)
		} else {
			logger.Debug("未检测到Turnstile")
		}
	} else {
		logger.Debug("检测到Turnstile")
	}

	return nil
}

// GetName 获取操作名称
func (a *WaitForTurnstileAction) GetName() string {
	return "WaitForTurnstile"
}
