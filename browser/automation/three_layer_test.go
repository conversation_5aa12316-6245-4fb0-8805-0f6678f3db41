package automation

import (
	"testing"

	"auto-register/browser"
	"auto-register/browser/automation/action"
	"auto-register/browser/automation/flow"
	"auto-register/browser/automation/step"
	"auto-register/config"
	"auto-register/generator"
)

func TestThreeLayerArchitecture(t *testing.T) {
	// 创建模拟配置
	cfg := &config.Config{
		BrowserHeadless:  true,
		BrowserUserAgent: "Mozilla/5.0 (Test)",
	}

	// 创建浏览器管理器
	manager := browser.NewManager(cfg)

	// 创建操作上下文
	ctx := action.NewActionContext(manager)

	// 测试Action层
	t.Run("ActionLayer", func(t *testing.T) {
		testActionLayer(t, ctx)
	})

	// 测试Step层
	t.Run("StepLayer", func(t *testing.T) {
		testStepLayer(t, ctx)
	})

	// 测试Flow层
	t.Run("FlowLayer", func(t *testing.T) {
		testFlowLayer(t, ctx)
	})
}

func testActionLayer(t *testing.T, ctx *action.ActionContext) {
	// 测试基础输入操作
	inputAction := action.NewInputElementAction("input[name='test']", "test value")
	if inputAction == nil {
		t.Error("NewInputElementAction 返回 nil")
	}
	if inputAction.GetName() != "InputElement" {
		t.Errorf("期望操作名称 'InputElement'，实际 '%s'", inputAction.GetName())
	}

	// 测试选择器输入操作
	selectors := action.ElementSelectors{"input[name='test']"}
	selectorInputAction := action.NewInputWithSelectorsAction(selectors, "test value")
	if selectorInputAction == nil {
		t.Error("NewInputWithSelectorsAction 返回 nil")
	}
	if selectorInputAction.GetName() != "InputWithSelectors" {
		t.Errorf("期望操作名称 'InputWithSelectors'，实际 '%s'", selectorInputAction.GetName())
	}

	// 测试按钮操作
	buttonAction := action.NewButtonElementAction("button[type='submit']")
	if buttonAction == nil {
		t.Error("NewButtonElementAction 返回 nil")
	}
	if buttonAction.GetName() != "ButtonElement" {
		t.Errorf("期望操作名称 'ButtonElement'，实际 '%s'", buttonAction.GetName())
	}

	// 测试导航操作
	navigateAction := action.NewNavigateAction("https://example.com")
	if navigateAction == nil {
		t.Error("NewNavigateAction 返回 nil")
	}
	if navigateAction.GetName() != "Navigate" {
		t.Errorf("期望操作名称 'Navigate'，实际 '%s'", navigateAction.GetName())
	}

	// 测试Turnstile操作
	turnstileAction := action.NewTurnstileAction()
	if turnstileAction == nil {
		t.Error("NewTurnstileAction 返回 nil")
	}
	if turnstileAction.GetName() != "Turnstile" {
		t.Errorf("期望操作名称 'Turnstile'，实际 '%s'", turnstileAction.GetName())
	}

	// 测试调试操作
	screenshotAction := action.NewScreenshotAction("test")
	if screenshotAction == nil {
		t.Error("NewScreenshotAction 返回 nil")
	}
	if screenshotAction.GetName() != "Screenshot" {
		t.Errorf("期望操作名称 'Screenshot'，实际 '%s'", screenshotAction.GetName())
	}
}

func testStepLayer(t *testing.T, ctx *action.ActionContext) {
	// 测试填写邮箱步骤
	fillEmailStep := step.NewFillEmailStep("<EMAIL>")
	if fillEmailStep == nil {
		t.Error("NewFillEmailStep 返回 nil")
	}
	if fillEmailStep.GetName() != "FillEmail" {
		t.Errorf("期望步骤名称 'FillEmail'，实际 '%s'", fillEmailStep.GetName())
	}

	// 测试填写用户名步骤
	fillUsernameStep := step.NewFillUsernameStep("testuser")
	if fillUsernameStep == nil {
		t.Error("NewFillUsernameStep 返回 nil")
	}
	if fillUsernameStep.GetName() != "FillUsername" {
		t.Errorf("期望步骤名称 'FillUsername'，实际 '%s'", fillUsernameStep.GetName())
	}

	// 测试填写密码步骤
	fillPasswordStep := step.NewFillPasswordStep("password123")
	if fillPasswordStep == nil {
		t.Error("NewFillPasswordStep 返回 nil")
	}
	if fillPasswordStep.GetName() != "FillPassword" {
		t.Errorf("期望步骤名称 'FillPassword'，实际 '%s'", fillPasswordStep.GetName())
	}

	// 测试点击继续步骤
	clickContinueStep := step.NewClickContinueStep()
	if clickContinueStep == nil {
		t.Error("NewClickContinueStep 返回 nil")
	}
	if clickContinueStep.GetName() != "ClickContinue" {
		t.Errorf("期望步骤名称 'ClickContinue'，实际 '%s'", clickContinueStep.GetName())
	}

	// 测试点击登录步骤
	clickLoginStep := step.NewClickLoginStep()
	if clickLoginStep == nil {
		t.Error("NewClickLoginStep 返回 nil")
	}
	if clickLoginStep.GetName() != "ClickLogin" {
		t.Errorf("期望步骤名称 'ClickLogin'，实际 '%s'", clickLoginStep.GetName())
	}

	// 测试导航步骤
	navigateStep := step.NewNavigateToLoginPageStep("https://example.com")
	if navigateStep == nil {
		t.Error("NewNavigateToLoginPageStep 返回 nil")
	}
	if navigateStep.GetName() != "NavigateToLoginPage" {
		t.Errorf("期望步骤名称 'NavigateToLoginPage'，实际 '%s'", navigateStep.GetName())
	}

	// 测试验证步骤
	verifyStep := step.NewVerifyLoginResultStep()
	if verifyStep == nil {
		t.Error("NewVerifyLoginResultStep 返回 nil")
	}
	if verifyStep.GetName() != "VerifyLoginResult" {
		t.Errorf("期望步骤名称 'VerifyLoginResult'，实际 '%s'", verifyStep.GetName())
	}

	// 测试步骤序列
	sequence := step.NewStepSequence("TestSequence")
	if sequence == nil {
		t.Error("NewStepSequence 返回 nil")
	}
	if sequence.GetName() != "StepSequence[TestSequence]" {
		t.Errorf("期望序列名称 'StepSequence[TestSequence]'，实际 '%s'", sequence.GetName())
	}

	// 测试添加步骤
	sequence.Add(fillEmailStep).Add(fillPasswordStep)
	if len(sequence.Steps) != 2 {
		t.Errorf("期望步骤数量 2，实际 %d", len(sequence.Steps))
	}
}

func testFlowLayer(t *testing.T, ctx *action.ActionContext) {
	// 测试Augment登录流程
	augmentFlow := flow.NewAugmentLoginFlow()
	if augmentFlow == nil {
		t.Error("NewAugmentLoginFlow 返回 nil")
	}
	if augmentFlow.GetName() != "AugmentLogin" {
		t.Errorf("期望流程名称 'AugmentLogin'，实际 '%s'", augmentFlow.GetName())
	}

	// 测试设置基础URL
	customURL := "https://custom.example.com"
	augmentFlow.WithBaseURL(customURL)
	if augmentFlow.BaseURL != customURL {
		t.Errorf("期望基础URL '%s'，实际 '%s'", customURL, augmentFlow.BaseURL)
	}

	// 测试Cursor注册流程
	cursorFlow := flow.NewCursorRegisterFlow()
	if cursorFlow == nil {
		t.Error("NewCursorRegisterFlow 返回 nil")
	}
	if cursorFlow.GetName() != "CursorRegister" {
		t.Errorf("期望流程名称 'CursorRegister'，实际 '%s'", cursorFlow.GetName())
	}

	// 测试账号信息
	account := &generator.AccountInfo{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "Test",
		LastName:  "User",
	}

	// 验证账号信息字段
	if account.Email != "<EMAIL>" {
		t.Errorf("期望邮箱 '<EMAIL>'，实际 '%s'", account.Email)
	}
	if account.Password != "password123" {
		t.Errorf("期望密码 'password123'，实际 '%s'", account.Password)
	}
	if account.FirstName != "Test" {
		t.Errorf("期望名字 'Test'，实际 '%s'", account.FirstName)
	}
	if account.LastName != "User" {
		t.Errorf("期望姓氏 'User'，实际 '%s'", account.LastName)
	}
}

// 基准测试
func BenchmarkActionCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = action.NewInputElementAction("input[name='test']", "test value")
	}
}

func BenchmarkStepCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = step.NewFillEmailStep("<EMAIL>")
	}
}

func BenchmarkFlowCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = flow.NewAugmentLoginFlow()
	}
}
