#!/bin/bash

# 测试新的Turnstile处理逻辑

echo "=== 测试新的Turnstile处理逻辑 ==="
echo ""

# 清理旧的日志和截图
echo "清理旧的调试文件..."
rm -f auto-register.log
rm -rf screenshots/*
rm -rf debug/*

echo "当前配置:"
echo "- 浏览器: 有界面模式 (可观察过程)"
echo "- 日志级别: debug"
echo "- 重试次数: 5"
echo "- 重试间隔: 10秒"
echo ""

echo "开始测试..."
echo "注意：浏览器窗口将会打开，您可以观察Turnstile验证过程"
echo ""

# 运行测试
echo "2" | timeout 180s ./auto-register

echo ""
echo "=== 测试完成 ==="
echo ""

# 分析结果
echo "分析结果:"
if [ -f "auto-register.log" ]; then
    echo ""
    echo "新方法尝试统计:"
    grep -c "方法1" auto-register.log 2>/dev/null && echo "  方法1尝试次数: $(grep -c "方法1" auto-register.log)" || echo "  方法1: 0次"
    grep -c "方法2" auto-register.log 2>/dev/null && echo "  方法2尝试次数: $(grep -c "方法2" auto-register.log)" || echo "  方法2: 0次"
    grep -c "方法3" auto-register.log 2>/dev/null && echo "  方法3尝试次数: $(grep -c "方法3" auto-register.log)" || echo "  方法3: 0次"
    grep -c "方法4" auto-register.log 2>/dev/null && echo "  方法4尝试次数: $(grep -c "方法4" auto-register.log)" || echo "  方法4: 0次"
    
    echo ""
    echo "详细日志:"
    tail -20 auto-register.log | grep -E "(方法|Turnstile|验证)"
fi

echo ""
echo "截图文件:"
ls -la screenshots/ 2>/dev/null || echo "无截图文件"

echo ""
echo "调试文件:"
ls -la debug/ 2>/dev/null || echo "无调试文件"
