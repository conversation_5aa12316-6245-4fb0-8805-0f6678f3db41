package action

import (
	"fmt"
	"strings"
	"time"

	"auto-register/logger"
)

// VerifyLoginResultAction 验证登录结果操作
type VerifyLoginResultAction struct {
	WaitTime         time.Duration
	SuccessURLs      []string
	SuccessTitles    []string
	ErrorSelectors   []string
	LoginURLs        []string
	MaxRetries       int
	RetryInterval    time.Duration
	TakeScreenshot   bool
	AllowUnknownStatus bool
}

// NewVerifyLoginResultAction 创建验证登录结果操作
func NewVerifyLoginResultAction() *VerifyLoginResultAction {
	return &VerifyLoginResultAction{
		WaitTime: 3 * time.Second,
		SuccessURLs: []string{
			"augmentcode.com",
		},
		SuccessTitles: []string{
			"dashboard", "home", "welcome", "augment",
		},
		ErrorSelectors: []string{
			".error", ".alert-error", "[role=\"alert\"]",
		},
		LoginURLs: []string{
			"login.augmentcode.com",
			"/u/login",
			"identifier",
		},
		MaxRetries:         1,
		RetryInterval:      5 * time.Second,
		TakeScreenshot:     true,
		AllowUnknownStatus: true,
	}
}

// WithWaitTime 设置等待时间
func (a *VerifyLoginResultAction) WithWaitTime(duration time.Duration) *VerifyLoginResultAction {
	a.WaitTime = duration
	return a
}

// WithSuccessURLs 设置成功URL模式
func (a *VerifyLoginResultAction) WithSuccessURLs(urls []string) *VerifyLoginResultAction {
	a.SuccessURLs = urls
	return a
}

// WithSuccessTitles 设置成功标题模式
func (a *VerifyLoginResultAction) WithSuccessTitles(titles []string) *VerifyLoginResultAction {
	a.SuccessTitles = titles
	return a
}

// WithRetries 设置重试选项
func (a *VerifyLoginResultAction) WithRetries(maxRetries int, interval time.Duration) *VerifyLoginResultAction {
	a.MaxRetries = maxRetries
	a.RetryInterval = interval
	return a
}

// WithAllowUnknownStatus 设置是否允许未知状态
func (a *VerifyLoginResultAction) WithAllowUnknownStatus(allow bool) *VerifyLoginResultAction {
	a.AllowUnknownStatus = allow
	return a
}

// Execute 执行验证登录结果操作
func (a *VerifyLoginResultAction) Execute(ctx *ActionContext) error {
	logger.Info("检查登录结果")

	// 等待页面跳转或加载完成
	if a.WaitTime > 0 {
		time.Sleep(a.WaitTime)
	}

	for retry := 0; retry <= a.MaxRetries; retry++ {
		if retry > 0 {
			logger.Infof("重试验证登录结果 (第 %d/%d 次)", retry, a.MaxRetries)
			time.Sleep(a.RetryInterval)
		}

		result, err := a.checkLoginStatus(ctx)
		if err != nil {
			logger.Warnf("检查登录状态失败: %v", err)
			continue
		}

		switch result.Status {
		case "success":
			logger.Info("登录成功")
			if a.TakeScreenshot {
				ctx.Manager.TakeScreenshot("login_success")
			}
			return nil

		case "error":
			logger.Errorf("登录失败: %s", result.Message)
			if a.TakeScreenshot {
				ctx.Manager.TakeScreenshot("login_error")
			}
			return fmt.Errorf("登录失败: %s", result.Message)

		case "still_login_page":
			logger.Warn("仍在登录页面")
			if a.TakeScreenshot {
				ctx.Manager.TakeScreenshot("login_still_on_login_page")
			}
			if retry == a.MaxRetries {
				return fmt.Errorf("登录失败：仍在登录页面")
			}

		case "unknown":
			logger.Warn("登录状态不明确")
			if retry == a.MaxRetries {
				if a.AllowUnknownStatus {
					logger.Warn("无法确定登录状态，默认认为成功")
					if a.TakeScreenshot {
						ctx.Manager.TakeScreenshot("login_status_unknown")
					}
					return nil
				} else {
					return fmt.Errorf("无法确定登录状态")
				}
			}
		}
	}

	return fmt.Errorf("验证登录结果失败，已达最大重试次数")
}

// LoginStatus 登录状态结果
type LoginStatus struct {
	Status  string // success, error, still_login_page, unknown
	Message string
	URL     string
	Title   string
}

// checkLoginStatus 检查登录状态
func (a *VerifyLoginResultAction) checkLoginStatus(ctx *ActionContext) (*LoginStatus, error) {
	// 获取页面信息
	script := `
		return {
			url: window.location.href,
			title: document.title,
			hasError: !!document.querySelector('.error, .alert-error, [role="alert"]'),
			errorText: document.querySelector('.error, .alert-error, [role="alert"]')?.textContent || ''
		};
	`

	var result map[string]interface{}
	if err := ctx.Manager.ExecuteScript(script, &result); err != nil {
		return nil, fmt.Errorf("获取页面信息失败: %w", err)
	}

	url := result["url"].(string)
	title := result["title"].(string)
	hasError := result["hasError"].(bool)
	errorText := result["errorText"].(string)

	logger.Infof("当前页面URL: %s", url)
	logger.Infof("当前页面标题: %s", title)

	status := &LoginStatus{
		URL:   url,
		Title: title,
	}

	// 检查是否有错误信息
	if hasError && strings.TrimSpace(errorText) != "" {
		status.Status = "error"
		status.Message = errorText
		return status, nil
	}

	// 检查是否还在登录页面
	for _, loginURL := range a.LoginURLs {
		if strings.Contains(url, loginURL) {
			status.Status = "still_login_page"
			status.Message = "仍在登录页面"
			return status, nil
		}
	}

	// 检查是否跳转到了成功页面
	for _, successURL := range a.SuccessURLs {
		if strings.Contains(url, successURL) && !strings.Contains(url, "login.") {
			status.Status = "success"
			status.Message = "已跳转到主页面"
			return status, nil
		}
	}

	// 检查页面标题是否表示成功
	for _, successTitle := range a.SuccessTitles {
		if strings.Contains(strings.ToLower(title), successTitle) {
			status.Status = "success"
			status.Message = "页面标题表示成功"
			return status, nil
		}
	}

	// 无法确定状态
	status.Status = "unknown"
	status.Message = "无法确定登录状态"
	return status, nil
}

// GetName 获取操作名称
func (a *VerifyLoginResultAction) GetName() string {
	return "VerifyLoginResult"
}

// WaitForElementAction 等待元素出现操作
type WaitForElementAction struct {
	Selectors   ElementSelectors
	Timeout     time.Duration
	Required    bool
	Description string
}

// NewWaitForElementAction 创建等待元素操作
func NewWaitForElementAction(selectors ElementSelectors, timeout time.Duration) *WaitForElementAction {
	return &WaitForElementAction{
		Selectors:   selectors,
		Timeout:     timeout,
		Required:    true,
		Description: "等待元素出现",
	}
}

// WithRequired 设置是否必需
func (a *WaitForElementAction) WithRequired(required bool) *WaitForElementAction {
	a.Required = required
	return a
}

// WithDescription 设置描述
func (a *WaitForElementAction) WithDescription(desc string) *WaitForElementAction {
	a.Description = desc
	return a
}

// Execute 执行等待元素操作
func (a *WaitForElementAction) Execute(ctx *ActionContext) error {
	logger.Debugf("%s (超时: %v)", a.Description, a.Timeout)

	_, err := a.Selectors.FindElement(ctx, a.Timeout)
	if err != nil {
		if a.Required {
			return fmt.Errorf("%s失败: %w", a.Description, err)
		} else {
			logger.Warnf("%s失败: %v", a.Description, err)
		}
	}

	return nil
}

// GetName 获取操作名称
func (a *WaitForElementAction) GetName() string {
	return "WaitForElement"
}
