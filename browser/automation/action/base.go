package action

import (
	"fmt"
	"time"

	"auto-register/browser"
	"auto-register/generator"
)

// ActionContext 操作上下文，包含所有操作需要的共享资源
type ActionContext struct {
	Manager *browser.Manager
}

// NewActionContext 创建新的操作上下文
func NewActionContext(manager *browser.Manager) *ActionContext {
	return &ActionContext{
		Manager: manager,
	}
}

// Action 基础操作接口
type Action interface {
	Execute(ctx *ActionContext) error
	GetName() string
}

// LoginCredentials 登录凭据
type LoginCredentials struct {
	Email    string
	Password string
}

// NewLoginCredentialsFromAccount 从账号信息创建登录凭据
func NewLoginCredentialsFromAccount(account *generator.AccountInfo) *LoginCredentials {
	return &LoginCredentials{
		Email:    account.Email,
		Password: account.Password,
	}
}

// ActionResult 操作结果
type ActionResult struct {
	Success bool
	Message string
	Data    map[string]interface{}
}

// NewSuccessResult 创建成功结果
func NewSuccessResult(message string) *ActionResult {
	return &ActionResult{
		Success: true,
		Message: message,
		Data:    make(map[string]interface{}),
	}
}

// NewErrorResult 创建错误结果
func NewErrorResult(message string) *ActionResult {
	return &ActionResult{
		Success: false,
		Message: message,
		Data:    make(map[string]interface{}),
	}
}

// WithData 添加数据到结果
func (r *ActionResult) WithData(key string, value interface{}) *ActionResult {
	r.Data[key] = value
	return r
}

// HumanLikeDelay 模拟人类操作的延迟
func HumanLikeDelay() {
	time.Sleep(time.Duration(1000+time.Now().UnixNano()%2000) * time.Millisecond)
}

// RandomDelay 随机延迟
func RandomDelay(min, max int) {
	if min >= max {
		return
	}
	delay := time.Duration(min+int(time.Now().UnixNano())%(max-min)) * time.Second
	time.Sleep(delay)
}

// ElementSelectors 元素选择器集合
type ElementSelectors []string

// CommonInputSelectors 常用输入框选择器
var CommonInputSelectors = ElementSelectors{
	`input[name="username"]`,
	`input[name="email"]`,
	`input[type="email"]`,
	`input[id="username"]`,
	`input[id="email"]`,
	`input[data-testid="username"]`,
	`input[data-testid="email"]`,
	`input[placeholder*="email" i]`,
	`input[placeholder*="username" i]`,
	`input[placeholder*="邮箱" i]`,
	`input[placeholder*="用户名" i]`,
}

// PasswordInputSelectors 密码输入框选择器
var PasswordInputSelectors = ElementSelectors{
	`input[name="password"]`,
	`input[type="password"]`,
	`input[id="password"]`,
	`input[data-testid="password"]`,
	`input[placeholder*="password" i]`,
	`input[placeholder*="密码" i]`,
}

// ContinueButtonSelectors 继续按钮选择器
var ContinueButtonSelectors = ElementSelectors{
	`button[type="submit"]`,
	`button[data-action-button-primary="true"]`,
	`button[name="action"]`,
	`input[type="submit"]`,
	`button:contains("Continue")`,
	`button:contains("继续")`,
	`button:contains("Next")`,
	`button:contains("下一步")`,
	`[data-testid="continue-button"]`,
	`[data-testid="submit-button"]`,
	`.auth0-lock-submit`,
	`.auth0-lock-submit-button`,
}

// LoginButtonSelectors 登录按钮选择器
var LoginButtonSelectors = ElementSelectors{
	`button[type="submit"]`,
	`button[data-action-button-primary="true"]`,
	`button[name="action"]`,
	`input[type="submit"]`,
	`button:contains("Log in")`,
	`button:contains("Login")`,
	`button:contains("Sign in")`,
	`button:contains("登录")`,
	`button:contains("Sign In")`,
	`[data-testid="login-button"]`,
	`[data-testid="submit-button"]`,
	`.auth0-lock-submit`,
	`.auth0-lock-submit-button`,
}

// FindElement 在选择器列表中查找第一个可用的元素
func (selectors ElementSelectors) FindElement(ctx *ActionContext, timeout time.Duration) (string, error) {
	for _, selector := range selectors {
		if err := ctx.Manager.WaitForElement(selector, timeout); err == nil {
			return selector, nil
		}
	}
	return "", fmt.Errorf("未找到匹配的元素")
}

// IsAnyPresent 检查是否有任何选择器匹配的元素存在
func (selectors ElementSelectors) IsAnyPresent(ctx *ActionContext) bool {
	for _, selector := range selectors {
		if ctx.Manager.IsElementPresent(selector) {
			return true
		}
	}
	return false
}
